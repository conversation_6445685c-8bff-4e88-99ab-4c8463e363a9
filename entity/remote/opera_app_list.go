/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const OperaAppListName = "OperaAppList"

// OperaAppList请求client
type OperaAppListClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewOperaAppListClient(context *easy.Context) *OperaAppListClient {
	client := &OperaAppListClient{}
	client.Name = OperaAppListName
	client.Context = context
	return client
}

// get 参数封装结构
type OperaAppListGet struct {
	Username        string `json:"username"`
	ProductlineName string `json:"productline_name"`
}

// header 参数封装结构
type OperaAppListHeader struct {
	ContentType   string `json:"Content-Type"`
	Authorization string `json:"Authorization"`
}

// 返回结果封装结构

// 执行函数发起请求
func (t *OperaAppListClient) Do(
	query *OperaAppListGet,
	header *OperaAppListHeader) error {
	t.Header = header
	t.Query = query

	err := t.UfcRequest(nil)
	return err
}

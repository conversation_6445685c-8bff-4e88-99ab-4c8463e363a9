/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const TaskName = "Task"

// Task请求client
type TaskClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewTaskClient(context *easy.Context) *TaskClient {
	client := &TaskClient{}
	client.Name = TaskName
	client.Context = context
	return client
}

// post 参数封装结构
type TaskPost struct {
	Username string `json:"username"`
}

// header 参数封装结构
type TaskHeader struct {
	Authorization string `json:"Authorization"`
	ContentType   string `json:"Content-Type"`
}

// 返回结果封装结构
type TaskResultData struct {
	easy.BaseDto
	TaskID int64 `json:"task_id"`
}

func (t *TaskResultData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type TaskResult struct {
	easy.UfcResult
	Data *TaskResultData `json:"data"`
}

func (t *TaskResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *TaskClient) Do(
	body *TaskPost,
	header *TaskHeader) (*TaskResult, error) {
	t.Header = header
	t.Body = body
	res := &TaskResult{}
	err := t.UfcRequest(res)
	return res, err
}

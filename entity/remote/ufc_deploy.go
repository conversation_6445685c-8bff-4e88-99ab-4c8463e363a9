/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const UfcDeployName = "UfcDeploy"

// UfcDeploy请求client
type UfcDeployClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewUfcDeployClient(context *easy.Context) *UfcDeployClient {
	client := &UfcDeployClient{}
	client.Name = UfcDeployName
	client.Context = context
	return client
}

// post 参数封装结构
type UfcDeployPost struct {
	Username    string `json:"username"`
	Target      string `json:"target"`
	RepaireType string `json:"repaire_type"`
	Reason      string `json:"reason"`
	Hosts       string `json:"hosts"`
}

// header 参数封装结构
type UfcDeployHeader struct {
	Cred string `json:"cred"`
}

// 返回结果封装结构

// 执行函数发起请求
func (t *UfcDeployClient) Do(
	body *UfcDeployPost,
	header *UfcDeployHeader) error {
	t.Header = header
	t.Body = body

	err := t.UfcRequest(nil)
	return err
}

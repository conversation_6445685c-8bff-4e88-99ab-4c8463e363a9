/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const NginxLogShowName = "NginxLogShow"

// NginxLogShow请求client
type NginxLogShowClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewNginxLogShowClient(context *easy.Context) *NginxLogShowClient {
	client := &NginxLogShowClient{}
	client.Name = NginxLogShowName
	client.Context = context
	return client
}

// get 参数封装结构
type NginxLogShowGet struct {
	Filename string `json:"filename"`
	Backend  string `json:"backend"`
}

// 返回结果封装结构

// 执行函数发起请求
func (t *NginxLogShowClient) Do(
	query *NginxLogShowGet) error {
	t.Query = query

	err := t.UfcRequest(nil)
	return err
}

module icode.baidu.com/baidu/netdisk/ufc-admin-easy

go 1.22

require (
	github.com/dustin/go-humanize v1.0.1
	github.com/stretchr/testify v1.10.0
	go.uber.org/ratelimit v0.3.1
	icode.baidu.com/baidu/netdisk/easy-go-sdk v0.0.0-20250811032819-03167b0a5a17
	icode.baidu.com/baidu/netdisk/pcs-go-lib v0.0.0-20230301033644-e6b335ae07db
	icode.baidu.com/baidu/netdisk/tangram-iface v0.0.0-20250811050011-9250c7eeb364
)

require (
	github.com/alicebob/miniredis/v2 v2.31.0 // indirect
	github.com/benbjohnson/clock v1.3.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/garyburd/redigo v1.6.4 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.24.0 // indirect
	github.com/jinzhu/copier v0.3.5 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/oleiade/reflections v1.0.1 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/rogpeppe/go-internal v1.13.1 // indirect
	golang.org/x/crypto v0.33.0 // indirect
	golang.org/x/net v0.35.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	icode.baidu.com/baidu/netdisk/easy-adapter v0.0.0-20250801044710-6150a00bbc7e // indirect
	icode.baidu.com/baidu/netdisk/easy-common v0.0.0-20231229091946-d81c3e61ef3b // indirect
	icode.baidu.com/baidu/netdisk/tangram v0.0.0-20250811033410-d7fe35ec4370 // indirect
	icode.baidu.com/baidu/netdisk/tangram-mock v0.0.0-20250811045321-78797efb3c51 // indirect
	icode.baidu.com/baidu/netdisk/tangram-replay-mock v0.0.0-20230904121732-e85f3a9b8672 // indirect
	icode.baidu.com/baidu/netdisk/tangram-third-party v0.0.0-20250526021450-2af171f6bac6 // indirect

)

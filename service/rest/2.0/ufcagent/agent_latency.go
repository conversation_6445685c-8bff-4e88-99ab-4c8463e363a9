// 统计配置更新耗时监控信息
package service

import (
	"encoding/json"
	"os"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"
	dto "icode.baidu.com/baidu/netdisk/ufc-admin-easy/dto/rest/2.0/ufcagent"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/netutil"
)

type WarnInfoPerRequest struct {
	IP                string `json:"ip"`
	Container         string `json:"container"`
	Mtime             int64  `json:"mtime"`
	WorkerLatency     int64  `json:"worker_latency"`
	PrivilegedLatency int64  `json:"privileged_latency"`
}

func NewWarnInfoPerRequest(ip string, mtime int64, workerLatency int64, privilegedLatency int64) *WarnInfoPerRequest {
	res := &WarnInfoPerRequest{
		IP:                ip,
		Mtime:             mtime,
		WorkerLatency:     workerLatency,
		PrivilegedLatency: privilegedLatency,
	}

	name := getMatrixID()
	if strings.TrimSpace(name) == "" {
		var err error
		name, err = os.Hostname()
		if err != nil {
			name = ""
		}
	}
	res.Container = name
	return res
}

func getMatrixID() string {
	return os.Getenv("MATRIX_INSTANCE_ID")
}

func (t *UfcagentService) DoRecordLatency(req *dto.HandleReqDto) error {
	if req.BodyDto == nil {
		return nil
	}

	clientIP := t.Context.Request.ClientIP()
	workerLatency := conv.ToInt64(req.BodyDto.WorkerLatency)
	privilegedLatency := conv.ToInt64(req.BodyDto.PrivilegedLatency)
	mtime := conv.ToInt64(req.BodyDto.Mtime)

	// 计算 total_latency = worker_latency + privileged_latency
	totalLatency := workerLatency + privilegedLatency

	// 全局 ZSet（保持原有逻辑）
	privilegedZSet := cacheutil.GetUFCPrivilegedLatencyZSetName()
	workerZSet := cacheutil.GetUFCWorkerLatencyZSetName()
	mtimeZSet := cacheutil.GetUFCIPMtimesZSetName()
	totalZSet := cacheutil.GetUFCTotalLatencyZSetName()

	if res, err := t.Redis.ZAdd(privilegedZSet, clientIP, privilegedLatency); err != nil {
		t.SLog.Warning("zadd privileged zset failed").Set("client ip", clientIP).Set("privileged latency", privilegedLatency).Set("res", res).SetErr(err).Print()
	}
	if res, err := t.Redis.ZAdd(workerZSet, clientIP, workerLatency); err != nil {
		t.SLog.Warning("zadd worker zset failed").Set("client ip", clientIP).Set("worker latency", workerLatency).Set("res", res).SetErr(err).Print()
	}
	if res, err := t.Redis.ZAdd(totalZSet, clientIP, totalLatency); err != nil {
		t.SLog.Warning("zadd total zset failed").Set("client ip", clientIP).Set("total latency", totalLatency).Set("res", res).SetErr(err).Print()
	}

	if res, err := t.Redis.ZAdd(mtimeZSet, clientIP, mtime); err != nil {
		t.SLog.Warning("record heartbeat failed").Set("client ip", clientIP).Set("res", res).SetErr(err).Print()
		return err
	}

	// 按机房分桶存储（新增逻辑）
	idc := netutil.GetIDCByIP(t.Context, clientIP)
	if idc != "unknown" {
		privilegedZSetByIDC := cacheutil.GetUFCPrivilegedLatencyZSetNameByIDC(idc)
		workerZSetByIDC := cacheutil.GetUFCWorkerLatencyZSetNameByIDC(idc)
		mtimeZSetByIDC := cacheutil.GetUFCIPMtimesZSetNameByIDC(idc)
		totalZSetByIDC := cacheutil.GetUFCTotalLatencyZSetNameByIDC(idc)

		if res, err := t.Redis.ZAdd(privilegedZSetByIDC, clientIP, privilegedLatency); err != nil {
			t.SLog.Warning("zadd privileged zset by idc failed").Set("client ip", clientIP).Set("idc", idc).Set("privileged latency", privilegedLatency).Set("res", res).SetErr(err).Print()
		}
		if res, err := t.Redis.ZAdd(workerZSetByIDC, clientIP, workerLatency); err != nil {
			t.SLog.Warning("zadd worker zset by idc failed").Set("client ip", clientIP).Set("idc", idc).Set("worker latency", workerLatency).Set("res", res).SetErr(err).Print()
		}
		if res, err := t.Redis.ZAdd(totalZSetByIDC, clientIP, totalLatency); err != nil {
			t.SLog.Warning("zadd total zset by idc failed").Set("client ip", clientIP).Set("idc", idc).Set("total latency", totalLatency).Set("res", res).SetErr(err).Print()
		}
		if res, err := t.Redis.ZAdd(mtimeZSetByIDC, clientIP, mtime); err != nil {
			t.SLog.Warning("zadd mtime zset by idc failed").Set("client ip", clientIP).Set("idc", idc).Set("mtime", mtime).Set("res", res).SetErr(err).Print()
		}
	}

	// 每个请求的统计先上报，后面使频率情况看是否关闭
	enable := conf.Application.ConfigUpdateStatEnable
	if enable && t.isExceedLimit(mtime, workerLatency, privilegedLatency) {
		t.SLog.Warning("exceed limit").Set("ip", clientIP).Set("mtime", mtime).
			Set("worker latency", workerLatency).Set("privileged latency", privilegedLatency).Set("enable", enable).Print()

		if conf.Application.EnableInPerRequest {
			// hi 报警
			t.statWarnInfo(clientIP, mtime, workerLatency, privilegedLatency)
		}
	}

	return nil
}

func (t *UfcagentService) isExceedLimit(mtime int64, workerLatency int64, privilegedLatency int64) bool {
	if workerLatency > 60 {
		return true
	}
	if privilegedLatency > 30*60 {
		return true
	}
	if mtime != 0 && mtime != -1 && time.Now().Unix()-mtime > 30*60 {
		return true

	}

	return false
}

func (t *UfcagentService) statWarnInfo(ip string, mtime int64, workerLatency int64, privilegedLatency int64) {
	warnInfo := NewWarnInfoPerRequest(ip, mtime, workerLatency, privilegedLatency)
	warnInfoByte, err := json.Marshal(warnInfo)
	if err != nil {
		t.SLog.Warning("statWarnInfo json marshal faield").SetErr(err).Print()
		return
	}

	hiInfo := "机器延迟监控异常: \n" + string(warnInfoByte)

	sendHiOption := common.SendHiOption{
		Type:    common.SendHiTypeText,
		Content: hiInfo,
	}
	if err := common.SendHi(conf.Application.HiWarning.URL, sendHiOption); err != nil {
		t.SLog.Warning("send hi failed").SetErr(err).Print()
		return
	}

}

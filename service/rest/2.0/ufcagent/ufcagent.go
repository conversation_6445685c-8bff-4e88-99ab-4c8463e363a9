/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: ufcagent业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"

	dtov2 "icode.baidu.com/baidu/netdisk/ufc-admin-easy/dto/rest/2.0/agent"
	dto "icode.baidu.com/baidu/netdisk/ufc-admin-easy/dto/rest/2.0/ufcagent"
	agentservice "icode.baidu.com/baidu/netdisk/ufc-admin-easy/service/rest/2.0/agent"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type UfcagentService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
}

// 初始化 必须传入上下文
func NewUfcagentService(ctx *easy.Context) *UfcagentService {
	service := &UfcagentService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * handle - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *UfcagentService) Handle(req *dto.HandleReqDto) (*dto.HandleResDto, error) {
	res := &dto.HandleResDto{}
	// 编写业务代码组装Response

	if req.BodyDto == nil {
		return res, nil
	}

	async := easy.NewAsync(t.Context)

	version := ""
	if req.BodyDto.UfcVersion != nil {
		version = conv.ToString(req.BodyDto.UfcVersion)
	}

	async.GoFunc(saveVersion, t.Context, version)

	// 端到端延迟监控上报
	if err := t.DoRecordLatency(req); err != nil {
		t.SLog.Warning("do record latency").Set("req", req).SetErr(err).Print()
	}

	forbid := req.BodyDto.Forbid
	// 处理封禁实例逻辑
	if err := t.DoForbid(forbid); err != nil {
		t.SLog.Error("do forbid error").SetErr(err).Print()
		return nil, err
	}

	// 收集 hash负载均衡失败的 case
	if req.BodyDto.BpHashErrService != nil {
		slice, ok := req.BodyDto.BpHashErrService.([]any)
		if ok {
			var sliceStr []string
			for _, item := range slice {
				itemStr, ok := item.(string)
				if ok {
					sliceStr = append(sliceStr, itemStr)
				}
			}

			agentService := agentservice.NewAgentService(t.Context)
			dto := &dtov2.UploadReqDto{}
			var uploadBodySlice []*dtov2.UploadBody
			for _, v := range sliceStr {
				uploadBodySlice = append(uploadBodySlice, &dtov2.UploadBody{
					Key: v,
				})
			}

			dto.BodyDto = uploadBodySlice
			_, err := agentService.Upload(dto)
			if err != nil {
				t.SLog.Error("upload error").SetErr(err).Print()
				return nil, err
			}
		}
	}

	// 返回Response
	return res, nil
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewUfcagentService 后都会走的逻辑
 */
func (t *UfcagentService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

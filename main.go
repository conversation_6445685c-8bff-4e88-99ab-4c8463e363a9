/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: APP入口函数
 */
package main

import (
	"math/rand"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_module"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
	router "icode.baidu.com/baidu/netdisk/ufc-admin-easy/routers"
)

var Module = tangram_module.NewModule(start, stop)

func start() error {
	// 初始化热更新配置
	conf.Init()

	err := easy.Start(router.SetRouter())
	if err != nil {
		return err
	}
	cacheutil.InitVariable()

	rand.Seed(time.Now().UnixNano())

	// 定时任务注册路由
	err = easy.ScheduledStart(router.SetSchedulerRouter())
	if err != nil {
		return err
	}

	return nil
}

func stop() {
	// do something after server close listen and before exit,not ensure it have enough time to exec finish.
}

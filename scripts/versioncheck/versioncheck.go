package versioncheck

import (
	"encoding/json"
	"fmt"
	"os"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

// 主动执行该函数，可以获取 diff 数据，线下执行时可以放在 main 函数中执行，但需注意把线下的 redis 配置改成线上才能成功获取

func DiffCheck(ctx *easy.Context, newVersion string) error {
	bnsIPs := GetBnsIPs(ctx)

	redis := easy.NewRedis(ctx, "redis")
	versionHSet := cacheutil.GetVersionHSetName()

	m, err := redis.HgetallReturnMap(versionHSet)
	if err != nil {
		ctx.SLog.Warning("get version map failed").SetErr(err).Print()
		return err
	}

	type IPVersionInfo struct {
		IP      string
		Version string
	}

	var slice []*IPVersionInfo
	var blankSlice []*IPVersionInfo

	var ipStr string
	var blankIPStr string

	for ip, version := range m {
		if version == newVersion {
			continue
		}

		if _, ok := bnsIPs[ip]; !ok {
			fmt.Println("not in bns ip list, ip:", ip)
			continue
		}

		/*
			if version == "" {
				info := &IPVersionInfo{
					IP:      ip,
					Version: version,
				}
				blankSlice = append(blankSlice, info)
				blankIPStr += ip + "\n"
				continue
			}*/

		info := &IPVersionInfo{
			IP:      ip,
			Version: version,
		}
		slice = append(slice, info)

		ipStr += ip + "\n"
	}

	if len(slice) == 0 {
		ctx.SLog.Warning("slice len is 0").Print()
		return nil
	}

	fmt.Println("ipStr len:", len(slice), "ipStr: \n", ipStr)

	sliceByte, err := json.Marshal(slice)
	if err != nil {
		ctx.SLog.Warning("json marshal failed").SetErr(err).Print()
		return err
	}

	if err := os.WriteFile("old_version_info.json", sliceByte, 0666); err != nil {
		ctx.SLog.Warning("write file failed").SetErr(err).Print()
		return err
	}

	blankSliceByte, err := json.Marshal(blankSlice)
	if err != nil {
		ctx.SLog.Warning("json marshal failed").SetErr(err).Print()
		return err
	}

	if err := os.WriteFile("old_blank_version_info.json", blankSliceByte, 0666); err != nil {
		ctx.SLog.Warning("write file failed").SetErr(err).Print()
		return err
	}

	os.WriteFile("blank_version_ip.txt", []byte(blankIPStr), 0666)

	return nil
}

func GetBnsIPs(ctx *easy.Context) map[string]struct{} {
	ufcBnsIPs := make(map[string]struct{}) // UFC BNS下的机器IP列表
	// 获取BNS下的IP列表
	var bnsList []string = conf.Application.UfcBns
	for _, bns := range bnsList {
		// 内部做了重试
		ips, err := common.GetBnsInstance(ctx, bns)
		if err != nil {
			ctx.SLog.Warning("get bns instance error").Set("bns", bns).Print()
			continue
		}

		for _, ip := range ips {
			ufcBnsIPs[ip] = struct{}{}
		}
	}

	ctx.SLog.Warning("ufc bns ips info").Set("len", len(ufcBnsIPs)).Print()
	return ufcBnsIPs
}

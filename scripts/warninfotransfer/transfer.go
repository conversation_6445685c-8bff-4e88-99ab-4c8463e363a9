package warninfotransfer

import (
	"encoding/json"
	"os"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/worker"
)

func Transfer(ctx *easy.Context) error {
	warnInfo, err := ReadWarnInfo(ctx)
	if err != nil {
		return err
	}

	s := &worker.AgentLatencyScheduler{}
	newWarnInfo := s.GetWarnInfoByHostnameWarnInfo(warnInfo)
	warnInfoByte, err := json.Marshal(newWarnInfo)
	if err != nil {
		return err
	}
	if err := os.WriteFile("p-6ufc-moniter-online-warn-info-ips.json", warnInfoByte, 0666); err != nil {
		return err
	}
	var str string

	for i, item := range newWarnInfo.NotUploadIPs {
		if i%180 == 0 {
			str += "\n\n----------------------------------------\n\n"
		}
		str += item + "\n"

	}

	return os.WriteFile("p-6ufc-moniter-online-warn-info-ips.txt", []byte(str), 0666)

}

func ReadWarnInfo(ctx *easy.Context) (worker.WarnInfo, error) {
	byt, err := os.ReadFile("p-6ufc-moniter-online-warn-info.json")
	if err != nil {
		return worker.WarnInfo{}, err
	}

	var warnInfo worker.WarnInfo
	err = json.Unmarshal(byt, &warnInfo)
	if err != nil {
		return worker.WarnInfo{}, err
	}
	return warnInfo, nil
}

[clients]
    [clients.Task]
    toService = "Task"
    uri = "/v1/task"
    method = "POST"
    postType = "query"
    isNac = false
    contentType = ""
    errnoField = ""
    [clients.MigrateTask]
    toService = "migrateTask"
    uri = "/v1/migrate_task"
    method = "PUT"
    postType = "query"
    isNac = false
    contentType = "application/x-www-form-urlencoded"
    errnoField = ""
    [clients.OperaAppList]
    toService = "operaAppList"
    uri = "/v1/applications"
    method = "GET"
    postType = "query"
    isNac = false
    contentType = ""
    errnoField = ""
    [clients.UfcDeploy]
    toService = "UfcDeploy"
    uri = "/api/paas/v1/machine/ufc/deploy"
    method = "POST"
    postType = "query"
    isNac = false
    contentType = "application/x-www-form-urlencoded"
    errnoField = ""
    [clients.ZhiBan]
    toService = "zhiBan"
    uri = "/api/open/v1/zhiban/search"
    method = "POST"
    postType = "json"
    isNac = false
    contentType = ""
    errnoField = ""
    [clients.NginxLogShow]
    toService = "nginxLogShow"
    uri = "/watchdog/nginxlog/show"
    method = "POST"
    postType = "query"
    isNac = false
    contentType = ""
    errnoField = ""

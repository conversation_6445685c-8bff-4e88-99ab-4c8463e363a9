# system 是默认的日志输出文件的 name ，用户不能删除，若有输出到不同配置文件的需求，可以在此处设置多个 logger 的配置
system:
  # type 设置此日志文件的输出类型，目前支持 file ｜ std ｜ pb 三种类型
  type: file
  # path 设置日志文件的存储文件路径
  path: ./log/system.log
  #  Level 定义日志级别，
  #  支持级别  EMERGENCY | ERROR | WARNING | NOTICE | INFO | DEBUG
  #  其中 EMERGENCY 级别的日志输出后会主动退出进程
  #  大于改级别的日志都不会输出，日志级别可以在运行时通过 tangram log edit log 动态修改
  level: INFO
  # format 设置日志 prefix ｜ header ｜ base info 的格式化格式
  format: '[%s=%s]'
  # long_path 设置日志打印时是否输出文件的全路径，默认只输出文件名，全路径将包括 import 路径
  long_path: false
  # can_rotate 设置日志文件是否可以开启日志切分，为保证线上磁盘空间和减轻日志检索的压力，用户应当都开启日志切分
  can_rotate: true
  # backup_count 设置保留每个日志历史文件的数量
  backup_count: 48
  # rotate_timer 设置日志切分时间间隔
  # 设置的时间需要带有时间单位："ns", "us" (or "µs"), "ms", "s", "m", "h"
  rotate_timer: 1h
  # suffix 可设置不同级别的日志输出到不同后缀的日志文件中，支持多级别设置
  # 此处设置当级别 <= WARNING 的日志，都将输出到 log.log.wf 文件中
  suffix:
    WARNING: .wf
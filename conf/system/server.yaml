# mars_server 是用户使用时为 server 起的一个唯一性名字，每个 server 的名字应当都不同
tangram_http_server:

  # server_type 设置该 server 配置启动的 server 类型，目前支持 mars_http ｜ rest_http 两种，都是 http/https 类型的 server
  server_type: net_http

  # middleware 设置 server 使用的服务治理类型，网盘服务应该默认使用 govern，其他服务可不写，目前 govern 主要支持 http 请求的语义级治理能力、以及全类型的重试策略
  middleware: builtin

  # network 设置 server 启动的 net 类型
  network: tcp

  # addr 设置 server 启动的地址，可以是端口号 "8057"、":8057"、"0.0.0.0:8057"，或者在 opera 部署时使用端口名 "main","port_N" 等
  # 对使用端口名的服务在启动时会由框架完成端口号的查找和替换，不需要用户再手动在启动前执行端口替换逻辑
  addr: 8058

  # gracefully_shutdown_time 设置进程收到退出信号(kill)时，server 最长延迟退出时间，用于保证 server 可以在处理完已建立连接的请求后再退出
  # 设置的时间需要带有时间单位："ns", "us" (or "µs"), "ms", "s", "m", "h"
  # 默认最长时间 1m
  gracefully_shutdown_time: 1m

tangram_ipc_uds:
  # server_type 设置该 server 配置启动的 server 类型，目前支持 mars_http ｜ rest_http 两种，都是 http/https 类型的 server
  server_type: net_listener

  # manual_start 定义 Server 由进程在使用时启动，非框架启动后统一监听，按需
  manual_start: true

  # network 设置 server 启动的 net 类型
  network: unix

  # addr 设置 server 启动的地址，可以是端口号 "8057"、":8057"、"0.0.0.0:8057"，或者在 opera 部署时使用端口名 "main","port_N" 等
  # 对使用端口名的服务在启动时会由框架完成端口号的查找和替换，不需要用户再手动在启动前执行端口替换逻辑
  addr: status/tangram.socket

  # gracefully_shutdown_time 设置进程收到退出信号(kill)时，server 最长延迟退出时间，用于保证 server 可以在处理完已建立连接的请求后再退出
  # 设置的时间需要带有时间单位："ns", "us" (or "µs"), "ms", "s", "m", "h"
  # 默认最长时间 1m
  gracefully_shutdown_time: 1m
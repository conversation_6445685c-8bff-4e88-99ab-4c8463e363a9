mapper:
  x-tangram-log-id:
    upstream:
      - field: Http-X-Isis-Logid
        from: HEADER
      - field: X-Yun-Logid64
        from: HEADER
      - field: logid
        from: QUERYSTRING
      - field: log_id
        from: QUERYSTRING
      - field: dp-logid
        from: QUERYSTRING
    downstream:
      - field: HTTP-X-ISIS-LOGID
  x-tangram-call-id:
    upstream:
      - field: Http-X-Isis-Callid
        from: HEADER
      - field: callid
        from: QUERYSTRING
    downstream:
      - field: HTTP-X-ISIS-CALLID
  x-tangram-from-srv:
    downstream:
      - field: X-UFC-SELF-SERVICE-NAME
  x-tangram-to-srv:
    downstream:
      - field: X-UFC-SERVICE-NAME
  x-tangram-deadline:
    upstream:
      - field: X-UFC-DEADLINE
        from: HEADER
        tag: deadline_to_deadline=1&opt=rm
    downstream:
      - field: X-UFC-RW-TIMEOUT
        tag: deadline_to_expire=1&suffix=ms

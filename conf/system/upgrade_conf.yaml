# 设置框架与云端更新配置内容
# name, app 在云控注册的服务名
name: UfcAdminEasy
# open 设置是否开启从云端同步配置文件
open: true
# method 设置请求云端时使用的 http method
method: POST
# interactive_service_name 设置请求云端使用的 interactive name
interactive_service_name: tangram_cloud_sync_backend
# url 设置云端配置同步的 path
url: /rest/2.0/pvcp/tangram?method=sync
# interval 设置与请求云端的时间间隔，当初次请求成功后，时间间隔采用由云端下发的配置
interval: 10s
# temp_dir 设置拉取云端配置文件时的临时文件路径，默认为 /tmp
temp_dir: ""
# archive_dir 设置保留历史配置文件的目录，默认为 conf_root/archive/<path>
archive_dir: ""
# md5_sum_file 设置保留配置文件版本和同步时间间隔的文件路径，默认为 conf_root/md5sum.yaml，由框架在启动并与云端同步成功后自动生成
md5_sum_file: ""
# keep_backup_count 设置历史配置文件版本保留个数，默认 10 个
keep_backup_count: 5
entries:
  tangram_builtin_frequent_limit:
    service_name: tangram_builtin_frequent_limit
    addressing:
      type: ufc
      from: tangramFrameUfcName
      to: pss-cloudcache-common-yq
      version: builtin
    connection:
      idle_timeout: 300s
      max_active: 100
      max_conn_lifetime: 600s
      max_idle: 50
      use_pool: true
      wait: false
    dialopt:
      network: tcp
    penetrate: false
    proto: redis
    retry:
      connect: 3
      max: 10
      readwrite: 3
    timeout:
      conn_timeout: 3s
      read_timeout: 3s
      write_timeout: 4s
  tangram_cloud_sync_backend:
    service_name: tangram_cloud_sync_backend
    addressing:
      type: ufc
      version: builtin
      from: tangramFrameUfcName
      to: pvcp-tangramControlPlane
    connection:
      idle_timeout: 90s
      max_conn_per_host: 0
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
    dialopt: { }
    penetrate: true
    proto: http
    retry:
      connect: 1
      max: 3
      readwrite: 1
      semantics: [ ]
    timeout:
      conn_timeout: 30s
      http_timeout: 30s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  dump_table_proxy:
    service_name: dump_table_proxy
    addressing:
      type: ufc
      version: builtin
      from: tangramFrameUfcName
      to: tangram-table-proxy
    connection:
      idle_timeout: 90s
      max_conn_per_host: 0
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
    dialopt:
      type: ""
    penetrate: false
    proto: http
    retry:
      connect: 1
      max: 3
      readwrite: 1
      semantics: [ ]
    timeout:
      conn_timeout: 30s
      http_timeout: 30s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
# conf.yaml 是框架运行时完成 runtime 初始化的主配置文件，与框架自生相关或由框架提供给用户能力的配置信息都在此完成定义
#
# debug 开启部分 debug 逻辑防止用户在线下代码编写时因为平台的问题导致无法运行
debug: false

# service_name 服务的 APP name ，其名字与云配置上注册的名字完全相同，
# 同时，用户在 opera 等 PaaS 平台注册服务时应当也使用与此相同的名字注册
service_name: UfcAdminEasy

# fork_timeout 设置进程更新时的启动超时时间，如果进程超过这个时间仍然未启动成功，将杀死进程，并将进程的状态设置为 fork_failed 对应的状态
fork_timeout: 1m

# module_conf_path 定义加载业务 SO 文件配置路径
module_conf_path:
  - common/module.yaml

# mesh_conf_path 定义访问下游服务的方式和地址以及超时设置等配置文件的路径
mesh_conf_path:
  - system/interactive.yaml
  - common/interactive.yaml

# logger_conf_path 定义日志数据输出配置文件的路径
logger_conf_path:
  - common/log.yaml
  - system/log.yaml

# server_conf_path 定义进程监听端口的 Server 的配置文件的路径
server_conf_path:
  - common/server.yaml
  - system/server.yaml

# governance_conf_path 定义服务治理的配置文件路径
governance_conf_path:
  builtin:
    - system/govern.yaml
    - common/govern.yaml

# heartbeat_conf_path 定义向云端发送心跳包数据的方式和配置文件路径
heartbeat_conf_path:
  builtin:
    - system/heartbeat.yaml

# action_conf_path 为单次任务执行工具的配置文件
action_conf_path:
  - system/action.yaml

# plugin_conf_path 为 tangram plugin 的配置文件
plugin_conf_path:
  - common/plugin.yaml

# distributed_task_config 为 分布式定时任务 的配置文件
distributed_task_config:
  - common/distributedTask.yaml

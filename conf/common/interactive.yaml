entries:
  Task:
    addressing:
      from: ufc-admin-easy
      to: api.sandbox.opera.baidu-int.com
      type: direct
      version: builtin
    connection:
      idle_timeout: 90s
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      type: ""
    penetrate: false
    proto: http
    retry:
      connect: 3
      max: 3
      readwrite: 3
      semantics: []
      to_service_name: OperaProxy#api_v1-task_test
    service_name: OperaProxy
    timeout:
      conn_timeout: 500ms
      http_timeout: 10s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  UfcDeploy:
    addressing:
      from: ufc-admin-easy
      to: tianwang.baidu-int.com
      type: direct
      version: builtin
    connection:
      idle_timeout: 90s
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      type: ""
    penetrate: true
    proto: http
    retry:
      connect: 3
      max: 3
      readwrite: 3
      semantics: []
      to_service_name: OperaProxy#api_v1-machine/ufc/deploy_test
    service_name: OperaProxy
    timeout:
      conn_timeout: 500ms
      http_timeout: 10s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  migrateTask:
    addressing:
      from: ufc-admin-easy
      to: api.sandbox.opera.baidu-int.com
      type: direct
      version: builtin
    connection:
      idle_timeout: 90s
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      type: ""
    penetrate: false
    proto: http
    retry:
      connect: 3
      max: 3
      readwrite: 3
      semantics: []
      to_service_name: OperaProxy#api_v1-migrate_task_test
    service_name: OperaProxy
    timeout:
      conn_timeout: 500ms
      http_timeout: 10s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  naclocal:
    addressing:
      from: UfcAdminEasy-NO-UFC
      to: 127.0.0.1:8840
      type: direct
      version: builtin
    connection:
      idle_timeout: 90s
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      type: ""
    penetrate: true
    proto: http
    retry:
      connect: 3
      max: 3
      readwrite: 3
      semantics: []
      to_service_name: NacRely#api_naclocal_test
    service_name: NacRely
    timeout:
      conn_timeout: 500ms
      http_timeout: 10s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  nacmaster:
    addressing:
      from: UfcAdminEasy-NO-UFC
      to: sandbox:netdisk-nac-master
      type: ufc
      version: builtin
    connection:
      idle_timeout: 90s
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      type: ""
    penetrate: false
    proto: http
    retry:
      connect: 3
      max: 3
      readwrite: 3
      semantics: []
      to_service_name: NacRely#api_nacmaster_test
    service_name: NacRely
    timeout:
      conn_timeout: 500ms
      http_timeout: 10s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  nginxLogShow:
    addressing:
      from: ufc-admin-easy
      to: ***********:80
      type: direct
      version: builtin
    connection:
      idle_timeout: 90s
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      type: ""
    penetrate: false
    proto: http
    retry:
      connect: 3
      max: 3
      readwrite: 3
      semantics: []
      to_service_name: OperaProxy#api_nginxlog-show-vip_test
    service_name: OperaProxy
    timeout:
      conn_timeout: 500ms
      http_timeout: 10s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  operaAppList:
    addressing:
      from: ufc-admin-easy
      to: api.sandbox.opera.baidu-int.com
      type: direct
      version: builtin
    connection:
      idle_timeout: 90s
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      type: ""
    penetrate: false
    proto: http
    retry:
      connect: 3
      max: 3
      readwrite: 3
      semantics: []
      to_service_name: OperaProxy#api_v1-applications_test
    service_name: OperaProxy
    timeout:
      conn_timeout: 500ms
      http_timeout: 10s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  redis:
    addressing:
      from: ufc-admin-easy
      to: sandbox:pss-cloudcache-common-bj
      type: ufc
      version: builtin
    connection:
      max_active: 100
      max_conn_lifetime: 30s
      max_idle: 20
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      network: tcp
      type: redis
    penetrate: false
    proto: redis
    retry:
      connect: 1
      max: 3
      readwrite: 2
      semantics: []
    service_name: UfcAdminEasy
    timeout:
      conn_timeout: 500ms
      read_timeout: 1s
      write_timeout: 3s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
  zhiBan:
    addressing:
      from: ufc-admin-easy
      to: zhiban.baidu-int.com
      type: direct
      version: builtin
    connection:
      idle_timeout: 90s
      max_idle: 100
      max_idle_per_host: 2
      use_pool: true
      wait: false
    depend_on: true
    dialopt:
      type: ""
    penetrate: true
    proto: http
    retry:
      connect: 3
      max: 3
      readwrite: 3
      semantics: []
      to_service_name: OperaProxy#api_zhiban-search_test
    service_name: OperaProxy
    timeout:
      conn_timeout: 500ms
      http_timeout: 10s
      keepalive_timeout: 30s
    transport:
      disable_compression: false
      disable_keepalive: false
      expect_continue_timeout: 1s
      max_resp_header_bytes: 10485760
      read_buffer_size: 4096
      resp_header_timeout: 0s
      write_buffer_size: 4096
self_service_name: UfcAdminEasy

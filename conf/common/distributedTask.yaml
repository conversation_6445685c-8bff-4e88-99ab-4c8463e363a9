#cloudcache key，业务自行申请
lock_key: p-6ufc-migratekey
#key过期时间
expire_time: 120s
#抢锁使用的redis service, 必须在interactive 配置中存在
redis_service_name: redis
#是否开启分布式任务
enable: true
#全局配置,无论有没有抢到锁所有实例都跑定时任务
all_instance_run: false
#定时任务是否阻塞进程退出（默认不阻塞）
sync_stop: false
#定时任务阻塞进程退出超时时间，没有配置默认一分钟
sync_timeout: 60s
#任务配置
tasks:
    migrate:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: false
        #任务运行间隔
        task_interval: 1h
        #任务开始时间（可以没有）
        task_start_time: 2023-07-12 00:10:00
        #crontab格式配置
        task_cron: ""
    sendMail:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: false
        #任务运行间隔
        task_interval: 24h
        #任务开始时间（可以没有）
        task_start_time: 2023-07-13 09:55:00
        #crontab格式配置
        task_cron: ""
    hiWarning:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: false
        #任务运行间隔
        task_interval: 10m
        #任务开始时间（可以没有）
        task_start_time: 2024-06-25 00:00:00
        #crontab格式配置
        task_cron: ""
    ufcMetaRightCheck:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: false
        #任务运行间隔
        task_interval: 3m
        #任务开始时间（可以没有）
        task_start_time: 2025-01-01 00:00:00
        #crontab格式配置
        task_cron: ""
    AgentLatency:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: false
        #任务运行间隔
        task_interval: 1h
        #任务开始时间（可以没有）
        task_start_time: 
        #crontab格式配置
        task_cron: ""
    WatchDog:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: true
        #任务运行间隔
        task_interval: 1h
        #任务开始时间（可以没有）
        task_start_time: 
        #crontab格式配置
        task_cron: ""
    UfcAgentLatency:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: false
        #任务运行间隔
        task_interval: 5m
        #任务开始时间（可以没有）
        task_start_time: 2025-06-05 00:00:00
        #crontab格式配置
        task_cron: ""
    WatchDogHi:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: false
        #任务运行间隔
        task_interval: 1h
        #任务开始时间（可以没有）
        task_start_time: 2025-07-01 10:10:00
        #crontab格式配置
        task_cron: ""
    smallFlowCheck:
        #任务是否开启
        enable: true
        #单个任务级别配置,true代表无论有没有抢到锁所有实例都跑这个定时任务
        all_instance_run: false
        #任务运行间隔
        task_interval: 1h
        #任务开始时间（可以没有）
        task_start_time: 
        #crontab格式配置
        task_cron: ""

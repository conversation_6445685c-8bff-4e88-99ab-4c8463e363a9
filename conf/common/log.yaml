access:
  backup_count: 48
  can_rotate: true
  format: '%s=%s'
  path: ./log/access
  rotate_timer: 1h
  type: file
default:
  backup_count: 48
  can_rotate: true
  format: '[%s=%s]'
  level: INFO
  long_path: false
  path: ./log/UfcAdminEasy.log
  rotate_timer: 1h
  suffix:
    WARNING: .wf
  type: file
downStream:
  backup_count: 48
  can_rotate: true
  format: '%s=%s'
  path: ./log/easy.log
  rotate_timer: 1h
  type: file

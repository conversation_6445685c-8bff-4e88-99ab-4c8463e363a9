package common

import (
	"fmt"
	"strconv"
	"time"
)

func GetStr(v interface{}) string {
	if v == nil {
		return ""
	}

	switch result := v.(type) {
	case int:
		return strconv.Itoa(result)
	case string:
		return result
	case []byte:
		return string(result)
	default:
		if v != nil {
			return fmt.Sprint(result)
		}
	}

	return ""
}

func IsTodayWeekend() bool {
	t := time.Now()
	weekday := t.Weekday()
	return weekday == time.Saturday || weekday == time.Sunday
}

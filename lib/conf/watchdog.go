/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 配置定义，热更新实现
 */
package conf

import (
	"errors"
	"sync"

	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
)

// watchdog
type WatchdogConf struct {
	Mu                sync.RWMutex
	fileName          string
	fileType          tangram_config.Decoder
	HiWarningEnabled  bool `json:"hi_warning_enabled" `
	HiWarningInterval int  `json:"hi_warning_interval" `
}

// 热更新Fetch
func (c *WatchdogConf) Fetch(data []byte) error {
	c.Mu.Lock()
	defer c.Mu.Unlock()
	if len(data) > 0 {
		err := tangram_config.Unmarshal(c.fileType, data, c)
		if err != nil {
			tangram_logger.Error("[msg:conf Fetch error] [filename: %s] [error: %v]", c.fileName, err)
			return err
		}
	} else {
		tangram_logger.Warning("[msg:conf Fetch error] [filename: %s] [error: data is empty]", c.fileName)
		return errors.New("data is empty")
	}
	return nil
}

/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 配置定义，热更新实现
 */
package conf

import (
	"errors"
	"sync"

	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
)

// 应用基础配置
type AppConfApp struct {
	ServerSignCret string `toml:"serverSignCret" `
	Appid          int    `toml:"appid" `
	Name           string `toml:"name" `
	VerifyOnOff    bool   `toml:"verifyOnOff" `
}

type AppConfCache struct {
	LocalExpire        int    `toml:"localExpire" `
	RedisPrefix        string `toml:"redisPrefix" `
	RedisDefaultName   string `toml:"redisDefaultName" `
	LocalCleanInterval int    `toml:"localCleanInterval" `
}

type AppConfAuth struct {
	BdussLen                    int    `toml:"bdussLen" `
	Tpl                         string `toml:"tpl" `
	TokenKey                    string `toml:"tokenKey" `
	DefaultEmptyHTTPHeaderValue string `toml:"defaultEmptyHttpHeaderValue" `
}

type AppConf struct {
	Mu       sync.RWMutex
	fileName string
	fileType tangram_config.Decoder
	App      *AppConfApp   `toml:"app" `
	Cache    *AppConfCache `toml:"cache" `
	Auth     *AppConfAuth  `toml:"auth" `
}

// 热更新Fetch
func (c *AppConf) Fetch(data []byte) error {
	c.Mu.Lock()
	defer c.Mu.Unlock()
	if len(data) > 0 {
		err := tangram_config.Unmarshal(c.fileType, data, c)
		if err != nil {
			tangram_logger.Error("[msg:conf Fetch error] [filename: %s] [error: %v]", c.fileName, err)
			return err
		}
	} else {
		tangram_logger.Warning("[msg:conf Fetch error] [filename: %s] [error: data is empty]", c.fileName)
		return errors.New("data is empty")
	}
	return nil
}

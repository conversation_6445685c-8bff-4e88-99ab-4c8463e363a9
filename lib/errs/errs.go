/*
* Easy生成，**平台修改本地update会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: 错误码
 */
package errs

import "icode.baidu.com/baidu/netdisk/easy-go-sdk"

// 框架公共错误码
var (
	// 参数异常
	CommonParams = easy.CustomError(2, "params error", 400)
	// 返回成功
	CommonSuccess = easy.CustomError(0, "success", 200)
	// 全部失败
	CommonAllFail = easy.CustomError(500, "all fail", 500)
	// 部分失败
	CommonPartFail = easy.CustomError(400, "partial fail", 400)
)

// 本项目公共错误码
var (
	AppParamMissing = easy.CustomError(400, "", 400)
)

package netutil

import (
	"context"
	"net"
	"os/exec"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// 指定内部 dns server
var defaultResolver = &net.Resolver{
	PreferGo: true, // 强制使用 Go 内部解析器
	Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
		// 这里指定 DNS 服务器，例如 Google 的 *******:53
		d := net.Dialer{Timeout: time.Second * 5} // 设置超时时间
		return d.DialContext(ctx, "udp", "***********:53")
	},
}

// IP 到 IDC 的缓存
var (
	ipToIDCCache = make(map[string]string)
	ipToIDCMutex sync.RWMutex
)

func GetHostnameByIP(ctx *easy.Context, clientIP string) string {
	var hostname []string
	var err error

	for i := 0; i < 3; i++ {
		hostname, err = defaultResolver.LookupAddr(context.Background(), clientIP)
		if err != nil {
			ctx.SLog.Warning("get hostname by ip error").Set("try times", i).Set("ip", clientIP).Set("hostname", hostname).SetErr(err).Print()
			continue
		}
		break
	}
	if err != nil {
		ctx.SLog.Error("get hostname by ip failed").SetErr(err).Print()
		return ""
	}

	if len(hostname) == 0 {
		return ""
	}

	return hostname[0]
}

func GetIPByHostname(ctx *easy.Context, hostname string) string {
	var ips []string
	var err error

	for i := 0; i < 3; i++ {
		ips, err = defaultResolver.LookupHost(context.Background(), hostname)
		if err != nil {
			ctx.SLog.Warning("get ip by hostname error").Set("try times", i).Set("hostname", hostname).Set("ip", ips).SetErr(err).Print()
			continue
		}
		break
	}
	if err != nil {
		ctx.SLog.Error("get ip by hostname failed").SetErr(err).Print()
		return ""
	}

	if len(ips) == 0 {
		return ""
	}

	return ips[0]
}

func IsOnlineInstance(ctx *easy.Context, ip string) bool {
	var execRes []byte
	var err error
	for i := 0; i < 3; i++ {
		execRes, err = exec.Command("get_service_by_host", "-i", ip).Output()
		if err != nil {
			ctx.SLog.Error("exe command get_service_by_host error").Set("try times", i).Set("ip", ip).SetErr(err).Print()
			continue
		}
		break
	}

	if err != nil {
		ctx.SLog.Error("exe command get_service_by_host failed").SetErr(err).Print()
		return false
	}

	execResStr := string(execRes)
	if strings.Contains(execResStr, "orp") || strings.Contains(execResStr, "opera") {
		return true
	}
	return false
}

// GetIDCByIP 通过 IP 获取机房信息，带缓存
func GetIDCByIP(ctx *easy.Context, ip string) string {
	// 先检查缓存
	ipToIDCMutex.RLock()
	if idc, exists := ipToIDCCache[ip]; exists {
		ipToIDCMutex.RUnlock()
		return idc
	}
	ipToIDCMutex.RUnlock()

	// 缓存中没有，需要查询
	hostname := GetHostnameByIP(ctx, ip)
	if hostname == "" {
		ctx.SLog.Warning("get hostname by ip failed").Set("ip", ip).Print()
		// 即使失败也缓存结果，避免重复查询
		ipToIDCMutex.Lock()
		ipToIDCCache[ip] = "unknown"
		ipToIDCMutex.Unlock()
		return "unknown"
	}

	idc := GetIDCByHostname(hostname)

	// 缓存结果
	ipToIDCMutex.Lock()
	ipToIDCCache[ip] = idc
	ipToIDCMutex.Unlock()

	return idc
}

// GetIDCByHostname 通过 hostname 获取机房信息
// 根据 hostname 用 . 分隔后的最后一个部分（物理IDC）映射到逻辑机房
func GetIDCByHostname(hostname string) string {
	if hostname == "" {
		return "unknown"
	}
	hostname = strings.TrimSuffix(hostname, ".baidu.com.")

	// 用 . 分隔 hostname，取最后一个部分作为物理IDC
	parts := strings.Split(hostname, ".")
	if len(parts) == 0 {
		return "unknown"
	}

	physicalIDC := strings.ToLower(parts[len(parts)-1])

	// 物理IDC到逻辑机房的映射
	idcMapping := map[string]string{
		"yq01": "yangquan",
		"yq02": "yangquan",
		"xaky": "xian",
		"xakd": "xian",
		"xafj": "xian",
	}

	// 查找映射
	if logicalIDC, exists := idcMapping[physicalIDC]; exists {
		return logicalIDC
	}

	// 默认返回 beijing（除了上述特定IDC外都是beijing）
	return "beijing"
}

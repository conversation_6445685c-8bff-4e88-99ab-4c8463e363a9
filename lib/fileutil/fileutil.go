package fileutil

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
)

const FileDir = "files"

func init() {
	// 初始化一个文件目录，所有文件都放在该目录下，创建失败也没有关系，每次请求会创建
	_ = os.MkdirAll(FileDir, 0755)

}

// 创建目录，等价于 `mkdir -p`
func MkdirAllFileDir() error {
	return os.MkdirAll(FileDir, 0755)
}

// 判断目录是否存在
func IsDirExist(dir string) bool {
	info, err := os.Stat(dir)
	if err != nil {
		return false
	}
	return info.IsDir()
}

// 判断文件是否存在且是普通文件
func IsFileExist(fileName string) error {
	info, err := os.Stat(fileName)
	if err != nil {
		if os.IsNotExist(err) {
			return errors.New("file does not exist: " + fileName)
		}
		return err
	}
	if info.IsDir() {
		return errors.New("path exists but is a directory, not a file: " + fileName)
	}
	return nil
}

// 读取文件内容
func GetFileContent(fileName string) ([]byte, error) {
	if err := IsFileExist(fileName); err != nil {
		return nil, err
	}
	return os.ReadFile(fileName)
}

// CreateAndWriteFile 创建带目录的文件，并写入内容（覆盖写）
func CreateAndWriteFile(filePath string, content []byte) error {
	// 提取目录部分
	dir := filepath.Dir(filePath)

	// 确保目录存在（等价于 mkdir -p）
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("create directory failed: %w", err)
	}

	// 创建文件（存在则清空）
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("create file failed: %w", err)
	}
	defer file.Close()

	// 写入内容
	if _, err := file.Write(content); err != nil {
		return fmt.Errorf("write file failed: %w", err)
	}

	return nil
}

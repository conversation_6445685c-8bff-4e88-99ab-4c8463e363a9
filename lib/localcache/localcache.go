package localcache

import "sync"

type LocalCache interface {
	Get(key string) int
	Incre(key string)
	Del(key string)
	Reset()
}

type localCache struct {
	cache map[string]int
	mu    sync.Mutex
}

var _ LocalCache = (*localCache)(nil)

var intance = &localCache{
	cache: make(map[string]int),
	mu:    sync.Mutex{},
}

func (l *localCache) Get(key string) int {
	l.mu.Lock()
	val := l.cache[key]
	l.mu.Unlock()
	return val
}

func (l *localCache) IncreBatch(keys []string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	for _, key := range keys {
		l.cache[key]++
	}
}

func (l *localCache) Incre(key string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.cache[key]++
}

func (l *localCache) Del(key string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	delete(l.cache, key)
}

func (l *localCache) Reset() {
	l.mu.Lock()
	defer l.mu.Unlock()
	for k := range l.cache {
		delete(l.cache, k)
	}
}

func (l *localCache) Range(fn func(k string, v int) error) error {
	l.mu.Lock()
	defer l.mu.Unlock()
	for k, v := range l.cache {
		if err := fn(k, v); err != nil {
			return err
		}
	}
	return nil
}

// 新一组 api
func Get(key string) int {
	return intance.Get(key)
}

func Incre(key string) {
	intance.Incre(key)
}

func IncreBatch(keys []string) {
	intance.IncreBatch(keys)
}

func Del(key string) {
	intance.Del(key)
}

func Range(fn func(k string, v int) error) error {
	return intance.Range(fn)
}
func Reset() {
	intance.Reset()
}

package hashutil

import (
	"fmt"
	"hash/fnv"
	"time"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

const (
	HashTablePrefix     = "forbid_backend"      // hashtable全称: forbid_backend-$service-$index
	HashTablePoolPrefix = "forbid_backend-set-" // 存储该小时内用到了哪些 hashtable

	// 存储每天需要进行实例迁移的节点，hashtable前缀，后接当天时间
	HashTableBackendPrefix = "forbid_backend-backend-"

	WhiteListAppSetName = "whitelist-app"

	DefaultHashTableSize = 100
)

// 获取存储当天需要封禁的节点信息，每次定时任务都是执行上一个整点小时的节点信息
// 在00:00 和 01:00 运行的任务得返回前一天的hashtable 名称
func GetCurDayHashTableForBackendName() string {
	suffix := time.Now().Add(-1 * time.Hour).Format("20060102")
	return HashTableBackendPrefix + suffix
}

// 每天的10:05分定时统计上一个小时的需要进行迁移的信息
func GetLastHourHashTableForBackendName() string {
	suffix := time.Now().Add(-1 * time.Hour).Format("2006010215")
	return HashTableBackendPrefix + suffix
}

// 用于第二天9.55时执行定时任务，获取昨天封禁信息
func GetLastDayHashTableForBackendName() string {
	suffix := time.Now().Add(-24 * time.Hour).Format("20060102")
	return HashTableBackendPrefix + suffix
}

func CurHourHashTableKey(serviceName string) (string, error) {
	return hashTableKey(serviceName, false)
}

func LastHourHashTableKey(serviceName string) (string, error) {
	return hashTableKey(serviceName, true)
}

func hashTableKey(serviceName string, isLastHour bool) (string, error) {
	hour, err := getHour(isLastHour)
	if err != nil {
		return "", err
	}
	index, err := Hash(serviceName)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-%s-%d-%s", HashTablePrefix, serviceName, index, hour), nil
}

func GetCurHashSet() (string, error) {
	return getHashPool(false)
}

// 上一个小时用到了哪些 hashtable
func GetLastHashSet() (string, error) {
	return getHashPool(true)
}

// hash表
func getHashPool(isLastHour bool) (string, error) {
	hour, err := getHour(isLastHour)
	if err != nil {
		return "", err
	}

	return HashTablePoolPrefix + hour, nil
}

func GetHashSetName(date string) string {
	// data demo: 20180101
	return HashTablePoolPrefix + date
}

func Hash(key string) (uint32, error) {
	hash := fnv.New32a()
	_, err := hash.Write([]byte(key))
	if err != nil {
		return 0, err
	}

	hashTableCnt := conf.Application.Migrate.RedisHashTableCntForService
	if hashTableCnt == 0 {
		hashTableCnt = DefaultHashTableSize
	}

	index := hash.Sum32() % conf.Application.Migrate.RedisHashTableCntForService
	return index, nil
}

func getHour(isLastHour bool) (string, error) {
	h, err := time.ParseDuration("-1h")
	if err != nil {
		//tangram_logger.Warning("[parse duration error] [err: %v]", err)
		return "", err
	}
	// 返回上一个小时
	if isLastHour {
		return time.Now().Add(1 * h).Format("2006010215"), nil
	}

	return time.Now().Format("2006010215"), nil
}

func GetWhiteListAppSetName() string {
	return WhiteListAppSetName
}

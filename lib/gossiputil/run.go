package gossiputil

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/netutil"
)

var idc = []string{
	"bjhw",
	"gajl",
	"yq01",
	"bdjl",
	"xafj",
	"xakd",
	"yq02",
	"bddwd",
	"bddx",
	"bjyz",
	"xaky",
}

var idcCounts = map[string]int{
	"bjhw":  740,
	"gajl":  125,
	"yq01":  8759,
	"bdjl":  3746,
	"xafj":  5340,
	"xakd":  3114,
	"yq02":  3638,
	"bddwd": 2111,
	"bddx":  1983,
	"bjyz":  971,
	"xaky":  4317,
}

type Config struct {
	Enable      bool           `json:"enable"`
	IDCGroupNum map[string]int `json:"idc_group_num"`
	Rate        float64        `json:"rate"`
	GroupNum    int            `json:"group_num"`
}

var defaultConfig = Config{
	Enable: true,
	IDCGroupNum: map[string]int{
		"default": 20,
		"gzhxy":   1,
		"bddwd":   10,
		"bddx":    7,
		"bjyz":    9,
		"st01":    3,
		"bjhw":    7,
	},
	Rate:     0.1,
	GroupNum: 20,
}

// Analaysis 函数用于执行分析操作
// 参数：
//
//	ctx *easy.Context: 上下文对象，包含请求相关的信息
//
// 返回值：
//
//	error: 如果函数执行过程中出现错误，将返回一个非nil的错误值，否则返回nil
func Analaysis(ctx *easy.Context) error {
	redis := easy.NewRedis(ctx, "redis")

	configMap, err := getGossipConfigMap(redis)
	if err != nil {
		ctx.SLog.Warning("analysis err").SetErr(err).Print()
		return err
	}

	instanceInfo, err := common.GetIdcInstanceInfo(ctx, "group.ufc-split-all.cloud-storage.all")
	if err != nil {
		ctx.SLog.Warning("get idc instance info error").SetErr(err).Print()
		return err
	}

	// 对于每个物理 idc 内，需要监控其 gossip 分组情况
	for phyIdc, _ := range instanceInfo.InstanceInfo {
		// 分析每个 idc 内部分组情况
		ufclogidcIdc := netutil.GetIDCByHostname(phyIdc)
		gossipConfig, ok := configMap[ufclogidcIdc]
		if !ok {
			continue
		}
		groupNum := getGroupNumByIDC(*gossipConfig, phyIdc)
		for i := 0; i < groupNum; i++ {
			key := fmt.Sprintf("p-3ufc-%s-%s-%d", ufclogidcIdc, phyIdc, i)
			scardCnt, err := redis.Scard(key)
			if err != nil {
				ctx.SLog.Warning("get scard error").SetErr(err).Print()
				return err
			}

			ctx.SLog.Info("idc gossip group info").
				Set("idc", phyIdc).
				Set("idc_instance_cnt", instanceInfo.InstanceCnt[phyIdc]).
				Set("redis_key", key).
				Set("group_num", groupNum).
				Set("group_index", i).
				Set("scard_cnt", scardCnt).
				Print()
		}

	}

	return nil
}

func getGroupNumByIDC(gossipConfig Config, idc string) int {
	num, ok := gossipConfig.IDCGroupNum[idc]
	if !ok {
		num = gossipConfig.GroupNum
	}
	return num

}

func getGossipConfigMap(redis *easy.Redis) (map[string]*Config, error) {
	idcs := []string{
		"beijing",
		"yangquan",
		"xian",
	}

	configMap := make(map[string]*Config)
	for _, idc := range idcs {
		config, err := getGossipConfigByIDC(redis, idc)
		if err != nil {
			return nil, err
		}
		configMap[idc] = config
	}
	return configMap, nil
}
func getGossipConfigByIDC(redis *easy.Redis, idc string) (*Config, error) {
	key := fmt.Sprintf("p-3ufc-%s-config-gossip", idc)
	value, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var config Config

	err = json.Unmarshal([]byte(value), &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}
	return &config, nil
}

func Main() {
	results := make(map[string]float64)
	for idc, count := range idcCounts {
		groupNum, exists := defaultConfig.IDCGroupNum[idc]
		if !exists {
			groupNum = defaultConfig.GroupNum
		}
		results[idc] = float64(count) / float64(groupNum)
	}

	for idc, rate := range results {
		fmt.Printf("%s: %f\n", idc, rate)
	}

	return
}

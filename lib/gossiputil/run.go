package gossiputil

import (
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/netutil"
)

var idc = []string{
	"bjhw",
	"gajl",
	"yq01",
	"bdjl",
	"xafj",
	"xakd",
	"yq02",
	"bddwd",
	"bddx",
	"bjyz",
	"xaky",
}

var idcCounts = map[string]int{
	"bjhw":  740,
	"gajl":  125,
	"yq01":  8759,
	"bdjl":  3746,
	"xafj":  5340,
	"xakd":  3114,
	"yq02":  3638,
	"bddwd": 2111,
	"bddx":  1983,
	"bjyz":  971,
	"xaky":  4317,
}

type Config struct {
	Enable      bool           `json:"enable"`
	IDCGroupNum map[string]int `json:"idc_group_num"`
	Rate        float64        `json:"rate"`
	GroupNum    int            `json:"group_num"`
}

// PhysicalIDCLatencyStats 物理IDC延迟统计结构
type PhysicalIDCLatencyStats struct {
	PhysicalIDC          string  `json:"physical_idc"`           // 物理机房名称，如 yq01, yq02, xaky 等
	MetricType           string  `json:"metric_type"`            // 指标类型：worker_latency, privileged_latency, agent_latency, total_latency
	P100                 float64 `json:"p100"`                   // P100 百分位（最大值）
	P99                  float64 `json:"p99"`                    // P99 百分位
	P95                  float64 `json:"p95"`                    // P95 百分位
	P90                  float64 `json:"p90"`                    // P90 百分位
	P80                  float64 `json:"p80"`                    // P80 百分位
	Median               float64 `json:"median"`                 // 中位数
	TrimmedMean          float64 `json:"trimmed_mean"`           // 去除最大最小各5个机器的平均数
	TotalCount           int     `json:"total_count"`            // 总机器数
	ExceedThresholdCount int     `json:"exceed_threshold_count"` // 超过阈值的机器数
	ThresholdValue       int64   `json:"threshold_value"`        // 阈值
	GossipGroupNum       int     `json:"gossip_group_num"`       // 该物理IDC的gossip组数
	AvgMachineNum        int     `json:"avg_machine_num"`        // 平均每组机器数
	Timestamp            int64   `json:"timestamp"`              // 统计时间戳
}

var defaultConfig = Config{
	Enable: true,
	IDCGroupNum: map[string]int{
		"default": 20,
		"gzhxy":   1,
		"bddwd":   10,
		"bddx":    7,
		"bjyz":    9,
		"st01":    3,
		"bjhw":    7,
	},
	Rate:     0.1,
	GroupNum: 20,
}

// Analaysis 函数用于执行分析操作
// 参数：
//
//	ctx *easy.Context: 上下文对象，包含请求相关的信息
//
// 返回值：
//
//	error: 如果函数执行过程中出现错误，将返回一个非nil的错误值，否则返回nil
func Analaysis(ctx *easy.Context) error {
	redis := easy.NewRedis(ctx, "redis")

	configMap, err := getGossipConfigMap(redis)
	if err != nil {
		ctx.SLog.Warning("analysis err").SetErr(err).Print()
		return err
	}

	instanceInfo, err := common.GetIdcInstanceInfo(ctx, "group.ufc-split-all.cloud-storage.all")
	if err != nil {
		ctx.SLog.Warning("get idc instance info error").SetErr(err).Print()
		return err
	}

	// 对于每个物理 idc 内，需要监控其 gossip 分组情况
	for phyIdc, _ := range instanceInfo.InstanceInfo {
		// 分析每个 idc 内部分组情况
		ufclogidcIdc := netutil.GetIDCByHostname(phyIdc)
		gossipConfig, ok := configMap[ufclogidcIdc]
		if !ok {
			continue
		}
		groupNum := getGroupNumByIDC(*gossipConfig, phyIdc)
		for i := 0; i < groupNum; i++ {
			key := fmt.Sprintf("p-3ufc-%s-%s-%d", ufclogidcIdc, phyIdc, i)
			scardCnt, err := redis.Scard(key)
			if err != nil {
				ctx.SLog.Warning("get scard error").SetErr(err).Print()
				return err
			}

			ctx.SLog.Info("idc gossip group info").
				Set("idc", phyIdc).
				Set("idc_instance_cnt", instanceInfo.InstanceCnt[phyIdc]).
				Set("redis_key", key).
				Set("group_num", groupNum).
				Set("group_index", i).
				Set("scard_cnt", scardCnt).
				Print()
		}

	}

	return nil
}

func getGroupNumByIDC(gossipConfig Config, idc string) int {
	num, ok := gossipConfig.IDCGroupNum[idc]
	if !ok {
		num = gossipConfig.GroupNum
	}
	return num

}

func getGossipConfigMap(redis *easy.Redis) (map[string]*Config, error) {
	idcs := []string{
		"beijing",
		"yangquan",
		"xian",
	}

	configMap := make(map[string]*Config)
	for _, idc := range idcs {
		config, err := getGossipConfigByIDC(redis, idc)
		if err != nil {
			return nil, err
		}
		configMap[idc] = config
	}
	return configMap, nil
}
func getGossipConfigByIDC(redis *easy.Redis, idc string) (*Config, error) {
	key := fmt.Sprintf("p-3ufc-%s-config-gossip", idc)
	value, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var config Config

	err = json.Unmarshal([]byte(value), &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}
	return &config, nil
}

// AnalysisPhysicalIDCLatency 分析每个物理IDC的延迟情况
// 参数：
//
//	ctx *easy.Context: 上下文对象，包含请求相关的信息
//
// 返回值：
//
//	error: 如果函数执行过程中出现错误，将返回一个非nil的错误值，否则返回nil
func AnalysisPhysicalIDCLatency(ctx *easy.Context) error {
	redis := easy.NewRedis(ctx, "redis")
	timestamp := time.Now().Unix()

	ctx.SLog.Info("Starting physical IDC latency analysis").Print()

	// 获取gossip配置信息
	configMap, err := getGossipConfigMap(redis)
	if err != nil {
		ctx.SLog.Warning("get gossip config map error").SetErr(err).Print()
		return err
	}

	// 获取实例信息
	instanceInfo, err := common.GetIdcInstanceInfo(ctx, "group.ufc-split-all.cloud-storage.all")
	if err != nil {
		ctx.SLog.Warning("get idc instance info error").SetErr(err).Print()
		return err
	}

	// 统计各种延迟类型
	metricTypes := []struct {
		name      string
		zsetKey   string
		threshold int64
	}{
		{"worker_latency", cacheutil.GetUFCWorkerLatencyZSetName(), 60},
		{"privileged_latency", cacheutil.GetUFCPrivilegedLatencyZSetName(), 60},
		{"total_latency", cacheutil.GetUFCTotalLatencyZSetName(), 120},
	}

	for _, metric := range metricTypes {
		ctx.SLog.Info("Processing metric type").Set("metric_type", metric.name).Print()

		stats, err := calculatePhysicalIDCLatencyStats(ctx, redis, metric.name, metric.zsetKey, metric.threshold, configMap, instanceInfo, timestamp)
		if err != nil {
			ctx.SLog.Warning("calculate physical IDC latency stats error").Set("metric_type", metric.name).SetErr(err).Print()
			continue
		}

		// 输出统计结果
		for _, stat := range stats {
			ctx.SLog.Info("physical IDC latency stats").
				Set("physical_idc", stat.PhysicalIDC).
				Set("metric_type", stat.MetricType).
				Set("avg_machine_num", stat.AvgMachineNum).
				Set("median", stat.Median).
				Set("trimmed_mean", stat.TrimmedMean).
				Set("p100", stat.P100).
				Set("p99", stat.P99).
				Set("p95", stat.P95).
				Set("p90", stat.P90).
				Set("p80", stat.P80).
				Set("total_count", stat.TotalCount).
				Set("exceed_threshold_count", stat.ExceedThresholdCount).
				Set("threshold_value", stat.ThresholdValue).
				Set("gossip_group_num", stat.GossipGroupNum).
				Set("timestamp", stat.Timestamp).Print()
		}
	}

	// 处理 Agent 延迟统计
	ctx.SLog.Info("Processing agent latency").Print()
	agentStats, err := calculatePhysicalIDCAgentLatencyStats(ctx, redis, cacheutil.GetUFCIPMtimesZSetName(), 300, configMap, instanceInfo, timestamp)
	if err != nil {
		ctx.SLog.Warning("calculate physical IDC agent latency stats error").SetErr(err).Print()
	} else {
		for _, stat := range agentStats {
			ctx.SLog.Info("physical IDC agent latency stats").
				Set("physical_idc", stat.PhysicalIDC).
				Set("metric_type", stat.MetricType).
				Set("p100", stat.P100).
				Set("p99", stat.P99).
				Set("p95", stat.P95).
				Set("p90", stat.P90).
				Set("p80", stat.P80).
				Set("total_count", stat.TotalCount).
				Set("exceed_threshold_count", stat.ExceedThresholdCount).
				Set("threshold_value", stat.ThresholdValue).
				Set("gossip_group_num", stat.GossipGroupNum).
				Set("timestamp", stat.Timestamp).Print()
		}
	}

	ctx.SLog.Info("Physical IDC latency analysis completed").Print()
	return nil
}

// calculatePhysicalIDCLatencyStats 计算物理IDC延迟统计
func calculatePhysicalIDCLatencyStats(ctx *easy.Context, redis *easy.Redis, metricType, zsetKey string, threshold int64, configMap map[string]*Config, instanceInfo *common.InstanceInfo, timestamp int64) ([]*PhysicalIDCLatencyStats, error) {
	// 获取 ZSet 中的所有数据
	slice, err := redis.ZRevRangeWithScores(zsetKey, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error").Set("zset", zsetKey).SetErr(err).Print()
		return nil, err
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no data in zset").Set("zset", zsetKey).Print()
		return nil, nil
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange with scores failed").Set("zset", zsetKey).Set("len", len(slice)).Print()
		return nil, fmt.Errorf("invalid zset data length")
	}

	// 按物理IDC分组数据
	physicalIDCData := make(map[string][]float64)
	totalCount := len(slice) / 2

	for i := 0; i < totalCount; i++ {
		ip := conv.ToString(slice[2*i])
		latency := conv.ToFloat64(slice[2*i+1])

		// 获取物理IDC
		physicalIDC := netutil.GetPhysicalIDCByIP(ctx, ip)
		if physicalIDC == "unknown" {
			continue
		}

		if physicalIDCData[physicalIDC] == nil {
			physicalIDCData[physicalIDC] = make([]float64, 0)
		}
		physicalIDCData[physicalIDC] = append(physicalIDCData[physicalIDC], latency)
	}

	// 计算每个物理IDC的统计数据
	var results []*PhysicalIDCLatencyStats
	for physicalIDC, latencies := range physicalIDCData {
		if len(latencies) == 0 {
			continue
		}

		// 排序
		sort.Float64s(latencies)

		// 计算超过阈值的数量
		exceedCount := 0
		for _, latency := range latencies {
			if latency > float64(threshold) {
				exceedCount++
			}
		}

		// 获取gossip组数
		gossipGroupNum := getGossipGroupNumByPhysicalIDC(physicalIDC, configMap, instanceInfo)

		stats := &PhysicalIDCLatencyStats{
			PhysicalIDC:          physicalIDC,
			MetricType:           metricType,
			TotalCount:           len(latencies),
			ExceedThresholdCount: exceedCount,
			ThresholdValue:       threshold,
			GossipGroupNum:       gossipGroupNum,
			AvgMachineNum:        len(latencies) / gossipGroupNum,

			Timestamp: timestamp,
		}

		// 计算百分位
		stats.P80 = calculatePercentile(latencies, 0.80)
		stats.P90 = calculatePercentile(latencies, 0.90)
		stats.P95 = calculatePercentile(latencies, 0.95)
		stats.P99 = calculatePercentile(latencies, 0.99)
		stats.P100 = calculatePercentile(latencies, 1.0)

		// 计算中位数
		stats.Median = calculatePercentile(latencies, 0.5)

		// 计算去除最大最小各5个机器的平均数
		if len(latencies) > 10 {
			trimmed := latencies[5 : len(latencies)-5]
			sum := 0.0
			for _, v := range trimmed {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(trimmed))
		} else {
			// 如果数据量不足，计算全部数据的平均数
			sum := 0.0
			for _, v := range latencies {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(latencies))
		}

		results = append(results, stats)
	}

	return results, nil
}

// calculatePhysicalIDCAgentLatencyStats 计算物理IDC Agent延迟统计（mtime 到当前时间的差值）
func calculatePhysicalIDCAgentLatencyStats(ctx *easy.Context, redis *easy.Redis, zsetKey string, threshold int64, configMap map[string]*Config, instanceInfo *common.InstanceInfo, currentTime int64) ([]*PhysicalIDCLatencyStats, error) {
	// 获取 ZSet 中的所有数据
	slice, err := redis.ZRevRangeWithScores(zsetKey, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error").Set("zset", zsetKey).SetErr(err).Print()
		return nil, err
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no data in zset").Set("zset", zsetKey).Print()
		return nil, nil
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange with scores failed").Set("zset", zsetKey).Set("len", len(slice)).Print()
		return nil, fmt.Errorf("invalid zset data length")
	}

	// 按物理IDC分组数据
	physicalIDCData := make(map[string][]float64)
	totalCount := len(slice) / 2

	for i := 0; i < totalCount; i++ {
		ip := conv.ToString(slice[2*i])
		mtime := conv.ToInt64(slice[2*i+1])

		if mtime <= 0 {
			// 跳过无效的 mtime
			continue
		}

		latency := currentTime - mtime
		if latency < 0 {
			latency = 0
		}

		// 获取物理IDC
		physicalIDC := netutil.GetPhysicalIDCByIP(ctx, ip)
		if physicalIDC == "unknown" {
			continue
		}

		if physicalIDCData[physicalIDC] == nil {
			physicalIDCData[physicalIDC] = make([]float64, 0)
		}
		physicalIDCData[physicalIDC] = append(physicalIDCData[physicalIDC], float64(latency))
	}

	// 计算每个物理IDC的统计数据
	var results []*PhysicalIDCLatencyStats
	for physicalIDC, latencies := range physicalIDCData {
		if len(latencies) == 0 {
			continue
		}

		// 排序
		sort.Float64s(latencies)

		// 计算超过阈值的数量
		exceedCount := 0
		for _, latency := range latencies {
			if latency > float64(threshold) {
				exceedCount++
			}
		}

		// 获取gossip组数
		gossipGroupNum := getGossipGroupNumByPhysicalIDC(physicalIDC, configMap, instanceInfo)

		stats := &PhysicalIDCLatencyStats{
			PhysicalIDC:          physicalIDC,
			MetricType:           "agent_latency",
			TotalCount:           len(latencies),
			ExceedThresholdCount: exceedCount,
			ThresholdValue:       threshold,
			GossipGroupNum:       gossipGroupNum,
			AvgMachineNum:        len(latencies) / gossipGroupNum,
			Timestamp:            currentTime,
		}

		// 计算百分位
		stats.P80 = calculatePercentile(latencies, 0.80)
		stats.P90 = calculatePercentile(latencies, 0.90)
		stats.P95 = calculatePercentile(latencies, 0.95)
		stats.P99 = calculatePercentile(latencies, 0.99)
		stats.P100 = calculatePercentile(latencies, 1.0)

		// 计算中位数
		stats.Median = calculatePercentile(latencies, 0.5)

		// 计算去除最大最小各5个机器的平均数
		if len(latencies) > 10 {
			trimmed := latencies[5 : len(latencies)-5]
			sum := 0.0
			for _, v := range trimmed {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(trimmed))
		} else {
			// 如果数据量不足，计算全部数据的平均数
			sum := 0.0
			for _, v := range latencies {
				sum += v
			}
			stats.TrimmedMean = sum / float64(len(latencies))
		}

		results = append(results, stats)
	}

	return results, nil
}

// calculatePercentile 计算百分位数
func calculatePercentile(sortedData []float64, percentile float64) float64 {
	if len(sortedData) == 0 {
		return 0
	}

	if len(sortedData) == 1 {
		return sortedData[0]
	}

	// 计算索引位置
	index := percentile * float64(len(sortedData)-1)
	lower := int(math.Floor(index))
	upper := int(math.Ceil(index))

	if upper >= len(sortedData) {
		upper = len(sortedData) - 1
	}
	if lower < 0 {
		lower = 0
	}

	if lower == upper {
		return sortedData[lower]
	}

	// 线性插值
	weight := index - float64(lower)
	return sortedData[lower]*(1-weight) + sortedData[upper]*weight
}

// 分析每个机房长尾数据，
func AnalysisLongTailResult(ctx *easy.Context, redis *easy.Redis, logicIDC string, tailCnt int) error {
	// zrange p-6ufc-moniter-total-latency-yangquan-zset -100 -1 WITHSCORES [WITH
	key := fmt.Sprintf("p-6ufc-moniter-total-latency-%s-zset", logicIDC)
	slice, err := redis.ZRangeWithScores(key, -tailCnt, -1)
	if err != nil {

	}

	return nil
}

// getGossipGroupNumByPhysicalIDC 获取物理IDC的gossip组数
func getGossipGroupNumByPhysicalIDC(physicalIDC string, configMap map[string]*Config, _ *common.InstanceInfo) int {
	// 首先通过物理IDC获取逻辑IDC
	logicalIDC := netutil.GetIDCByHostname(physicalIDC + ".baidu.com")

	// 获取该逻辑IDC的gossip配置
	gossipConfig, ok := configMap[logicalIDC]
	if !ok {
		// 如果没有配置，返回默认值
		return defaultConfig.GroupNum
	}

	// 获取该物理IDC的组数配置
	groupNum := getGroupNumByIDC(*gossipConfig, physicalIDC)
	return groupNum
}

package gossiputil

import (
	"testing"
)

// TestAnalysisPhysicalIDCLatency 测试物理IDC延迟分析功能
func TestAnalysisPhysicalIDCLatency(t *testing.T) {
	// 跳过这个测试，因为需要实际的Redis连接和配置
	t.<PERSON><PERSON>("Skipping TestAnalysisPhysicalIDCLatency - requires Redis connection and configuration")

	// 在实际环境中使用时的代码示例：
	// ctx := easy.NewContext()
	// err := AnalysisPhysicalIDCLatency(ctx)
	// if err != nil {
	//     t.Logf("AnalysisPhysicalIDCLatency returned error: %v", err)
	// } else {
	//     t.Log("AnalysisPhysicalIDCLatency completed successfully")
	// }
}

// TestCalculatePercentile 测试百分位计算函数
func TestCalculatePercentile(t *testing.T) {
	// 测试数据
	data := []float64{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0}

	// 测试各种百分位
	tests := []struct {
		percentile float64
		expected   float64
	}{
		{0.50, 5.5},  // P50 (中位数)
		{0.80, 8.2},  // P80
		{0.90, 9.1},  // P90
		{0.95, 9.55}, // P95
		{0.99, 9.91}, // P99
		{1.0, 10.0},  // P100 (最大值)
	}

	for _, test := range tests {
		result := calculatePercentile(data, test.percentile)
		// 使用浮点数比较的容差
		tolerance := 0.01
		if result < test.expected-tolerance || result > test.expected+tolerance {
			t.Errorf("calculatePercentile(data, %.2f) = %.2f, expected %.2f",
				test.percentile, result, test.expected)
		}
	}
}

// TestGetGossipGroupNumByPhysicalIDC 测试获取物理IDC的gossip组数
func TestGetGossipGroupNumByPhysicalIDC(t *testing.T) {
	// 创建测试配置，包含所有逻辑IDC
	configMap := map[string]*Config{
		"yangquan": {
			Enable: true,
			IDCGroupNum: map[string]int{
				"yq01": 10,
				"yq02": 8,
			},
			GroupNum: 20,
		},
		"xian": {
			Enable: true,
			IDCGroupNum: map[string]int{
				"xaky": 15,
			},
			GroupNum: 20,
		},
		"beijing": {
			Enable: true,
			IDCGroupNum: map[string]int{
				"bjhw": 12,
			},
			GroupNum: 20,
		},
	}

	// 测试用例
	tests := []struct {
		physicalIDC string
		expected    int
	}{
		{"yq01", 10}, // yangquan 逻辑IDC，有具体配置
		{"yq02", 8},  // yangquan 逻辑IDC，有具体配置
		{"xaky", 15}, // xian 逻辑IDC，有具体配置
		{"xakd", 20}, // xian 逻辑IDC，使用默认值
		{"bjhw", 12}, // beijing 逻辑IDC，有具体配置
		{"bdjl", 20}, // beijing 逻辑IDC，使用默认值
	}

	for _, test := range tests {
		result := getGossipGroupNumByPhysicalIDC(test.physicalIDC, configMap, nil)
		if result != test.expected {
			t.Errorf("getGossipGroupNumByPhysicalIDC(%s) = %d, expected %d",
				test.physicalIDC, result, test.expected)
		}
	}
}

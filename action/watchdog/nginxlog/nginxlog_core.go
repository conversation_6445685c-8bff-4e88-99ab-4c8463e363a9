/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: nginxlog模块的业务入口
 * 函数命名规则：接口名首字母大写+Core 框架会自动调用
 * 函数实现内容：1.特殊参数验证如raw 2.调用service执行业务逻辑 3.封装Response
 */
package action

// show接口业务入口
func (t *NginxlogController) ShowCore() {
	// 自定义验证参数 例如：t.ShowDto.Body

	// 调用service 获取ResponseDto
	byt, errDto := t.NginxlogService.Show(t.ShowDto)
	// 编写业务代码组装Response，业务可以通过配置自定义返回结构，详情可参见如下文档链接或通过t.Response.Output()之类
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/0pbQON0Sqnwzfm#anchor-dc9f7b40-5a56-11ed-804a-7302eed97af5
	// 绑定Response 返回
	if errDto != nil {
		t.SLog.Error("nginx log service show failed").Set("filename", t.ShowDto.Filename).Set("backend", t.ShowDto.Backend).SetErr(errDto).Print()
		t.SetResponse(byt, errDto)
		return
	}

	t.Response.SetBody(byt)
}

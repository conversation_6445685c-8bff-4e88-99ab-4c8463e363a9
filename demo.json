[{"id": 6228, "agid": 2347, "gid": 939, "desc": "6228", "wf": {"api_definition": {"desc": "验证用户身份并执行抽奖流程", "name": "*先验证用户是否通过学生认证\n*如果通过返回抽奖次数\n用户信息和分享文件信息\n如果该用户是学生调用抽奖接口进行抽奖，返回抽奖数据", "body": "[{\"key\":1,\"field\":\"root\",\"type\":\"struct\",\"desc\":\"主体请求结构\",\"validations\":[],\"children\":[{\"key\":2,\"field\":\"clienttype\",\"type\":\"string\",\"desc\":\"客户端类型\",\"validations\":[\"required\"]},{\"key\":3,\"field\":\"channel\",\"type\":\"string\",\"desc\":\"渠道\",\"validations\":[\"required\"]},{\"key\":4,\"field\":\"tid\",\"type\":\"string\",\"desc\":\"抽奖组件化tid\",\"validations\":[\"required\"]},{\"key\":5,\"field\":\"comp_ver\",\"type\":\"number\",\"desc\":\"组件版本\",\"validations\":[\"required\",\"integer\"]},{\"key\":6,\"field\":\"uid\",\"type\":\"string\",\"desc\":\"用户ID\",\"validations\":[\"required\"]},{\"key\":7,\"field\":\"action\",\"type\":\"string\",\"desc\":\"动作\",\"validations\":[\"required\"]}]}]", "method": "POST", "post_data_type": "2", "template": "[{\"key\":0,\"field\":\"root\",\"type\":\"struct\",\"typeid\":0,\"desc\":\"响应主体结构\",\"children\":[{\"key\":1,\"field\":\"lottery_num\",\"type\":\"number\",\"desc\":\"抽奖次数\",\"children\":null},{\"key\":2,\"field\":\"uid\",\"type\":\"string\",\"desc\":\"用户标识\",\"children\":null},{\"key\":3,\"field\":\"name\",\"type\":\"string\",\"desc\":\"用户昵称\",\"children\":null},{\"key\":4,\"field\":\"count\",\"type\":\"string\",\"desc\":\"抽奖次数\",\"children\":null},{\"key\":5,\"field\":\"isvip\",\"type\":\"string\",\"desc\":\"是否是普通VIP\",\"children\":null},{\"key\":6,\"field\":\"issvip\",\"type\":\"string\",\"desc\":\"是否是SVIP\",\"children\":null},{\"key\":7,\"field\":\"type\",\"type\":\"string\",\"desc\":\"活动类型：1抽奖2大富翁\",\"children\":null},{\"key\":8,\"field\":\"status\",\"type\":\"string\",\"desc\":\"用户状态 1 正常2 不正常\",\"children\":null},{\"key\":9,\"field\":\"age\",\"type\":\"string\",\"desc\":\"年龄\",\"children\":null},{\"key\":10,\"field\":\"file_info\",\"type\":\"struct\",\"desc\":\"用户分享文件信息\",\"children\":[{\"key\":11,\"field\":\"fid\",\"type\":\"string\",\"desc\":\"文件ID\",\"children\":null},{\"key\":12,\"field\":\"fname\",\"type\":\"string\",\"desc\":\"文件名称\",\"children\":null},{\"key\":13,\"field\":\"path\",\"type\":\"string\",\"desc\":\"文件路径\",\"children\":null}]},{\"key\":14,\"field\":\"ext\",\"type\":\"string\",\"desc\":\"扩展字段\",\"children\":null}]}]", "header_data": "[{\"field\":\"Content-Type\",\"comment\":\"内容类型\",\"type\":\"string\",\"validations\":[\"required\",\"oneof=application/x-www-form-urlencoded multipart/form-data application/json\"]}]", "gid": 939, "agid": 2347, "owner": "wangjun27", "cuser": "wangjun27", "muser": "wangjun27", "path": "lotteryfrequencyv2", "gname": "", "is_nac": "", "is_multi_req": 0, "is_multi_res": 0, "status": 0, "off_escape": 0, "query": "", "get_data": "[]", "post_data": "[]", "req_items": "[]", "res_items": "[]", "raw_type": "json", "is_req_ext": 0, "req_ext": "[]", "raw_example": "{}", "response_errno_fields": "errno", "res_example": "{}", "err_ids": "[]"}, "logic_description": {"description": "验证用户身份并执行抽奖流程", "steps": [{"step": 1, "next_step": 2, "action": "请求http协议下游", "description": "调用学生状态认证API验证用户是否通过学生认证", "extra_data": {"identify_name": "checkStudentStatus", "app_name": "UserAuth", "downstream": "/act/v2/collegestudents/getstatus", "group_name": "student-auth", "multi_name": "", "rpc_items": [{"left_value_type": "Body", "left_value": "uid", "right_item": {"type": "REQ", "data": {"type": "Param", "value": "uid"}}}]}}, {"step": 2, "next_step": 3, "action": "请求redis协议下游", "description": "查询Redis缓存中是否有用户抽奖次数信息", "extra_data": {"identify_name": "queryLotteryCountFromRedis", "cmd": "Get", "address": "lottery_count", "key": "lottery_count_%d", "placeholder_binding": [{"type": "REQ", "data": {"type": "Param", "value": "uid"}}]}}, {"step": 3, "next_step": 4, "action": "请求db协议下游", "description": "查询数据库获取抽奖次数信息", "extra_data": {"identify_name": "queryLotteryCountFromDB", "table_name": "activity_prize_deal", "description": "查询用户抽奖次数", "type": "info", "func_name": "GetLotteryCount", "sql_stmt": "SELECT count(*) FROM activity_prize_deal WHERE userid = ?", "func_type": "count", "field": "count"}}, {"step": 4, "next_step": 5, "action": "分支组件", "description": "根据抽奖次数执行相应逻辑", "extra_data": "", "if_group": {"branches": [{"cur_step": 4.1, "next_step": 4.2, "extra_data": {"type": "if", "expr": "lotteryCount > 10"}, "steps": [{"step": 4.1, "action": "输出日志", "description": "抽奖次数超过限制，暂停抽奖", "extra_data": {"log_level": "Warning", "log_msg": "抽奖次数超过10次，暂停抽奖", "slog_items": []}}]}, {"cur_step": 4.2, "next_step": 5, "extra_data": {"type": "if", "expr": "lotteryCount == 5"}, "steps": [{"step": 4.2, "action": "输出日志", "description": "抽奖次数等于5，上报信息预警", "extra_data": {"log_level": "Notice", "log_msg": "抽奖次数达到5次，进行信息预警", "slog_items": []}}]}]}}, {"step": 5, "next_step": 6, "action": "请求http协议下游", "description": "获取用户详细信息", "extra_data": {"identify_name": "getUserInfo", "app_name": "UserInfoService", "downstream": "/internal/act/v2/uinfo", "group_name": "user-info", "multi_name": "", "rpc_items": [{"left_value_type": "Query", "left_value": "uid", "right_item": {"type": "REQ", "data": {"type": "Param", "value": "uid"}}}]}}, {"step": 6, "action": "正常返回", "description": "返回用户详细信息，并附上本次活动用户获奖的分享文件信息", "extra_data": {"is_multi_res": false, "multi_key": "", "normal_items": [{"left_value": "lottery_num", "right_item": {"type": "VAR", "data": {"identify_name": "lotteryCount"}}}, {"left_value": "uid", "right_item": {"type": "REQ", "data": {"type": "Param", "value": "uid"}}}, {"left_value": "name", "right_item": {"type": "RPC", "data": {"identify_name": "getUserInfo", "field": "data.name"}}}, {"left_value": "file_info", "right_item": {"type": "CONST", "data": "File information and path here"}}]}}]}}}]
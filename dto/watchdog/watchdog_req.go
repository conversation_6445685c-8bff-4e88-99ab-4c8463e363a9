/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口入参封装dto
 * 结构体中的tag描述（由平台配置，无需手动修改）：
 * validate: 验证规则，多个规则由逗号分割
 * len：字符串限制长度
 * verify：加入参数验签 通过sign算法，框架自动验证
 * type:"raw" 标识json 格式的参数 通过raw字符串序列化
 */
package watchdog

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

type AlertReqDto struct {
	easy.RequestDto

	BodyDto *AlertBody `json:"-" type:"raw" validate:""`
}

type AlertBody struct {
	Type      string                 `json:"type" validate:""`
	Level     string                 `json:"level" validate:""`
	Title     string                 `json:"title" validate:""`
	Message   string                 `json:"message" validate:""`
	Timestamp string                 `json:"timestamp" validate:""`
	Hostname  string                 `json:"hostname" validate:""`
	Details   map[string]interface{} `json:"details" validate:""`
}

func (r *AlertReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}

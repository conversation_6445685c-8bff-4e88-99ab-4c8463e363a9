Global:
  version: 2.0
  group_email: <EMAIL>   # <------ 配置团队邮箱地址，用于接收xx.latest软件版本升级通知邮件

Default:
  profile: [release]

Profiles:
  - profile:
    name: release
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - go: 1.22.latest
    build:
      command: git clone ssh://*******************:8235/baidu/netdisk/tangram-make && sh tangram-make/build.sh tangram.makefile
    artifacts:
      release: true
  - profile:
    name: cov
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - go: 1.22.latest
    build:
      gcover:
        enable: true
      command: git clone ssh://*******************:8235/baidu/netdisk/tangram-make && sh tangram-make/cov.sh tangram.makefile
    artifacts:
      release: true
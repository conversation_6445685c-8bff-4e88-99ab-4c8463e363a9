/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 路由配置
 */
package router

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	_ "icode.baidu.com/baidu/netdisk/ufc-admin-easy/action" // 如果在action中设置了钩子必须导包
	agent "icode.baidu.com/baidu/netdisk/ufc-admin-easy/action/rest/2.0/agent"
	ufcagent "icode.baidu.com/baidu/netdisk/ufc-admin-easy/action/rest/2.0/ufcagent"
	watchdog "icode.baidu.com/baidu/netdisk/ufc-admin-easy/action/watchdog"
	nginxlog "icode.baidu.com/baidu/netdisk/ufc-admin-easy/action/watchdog/nginxlog"
	worker "icode.baidu.com/baidu/netdisk/ufc-admin-easy/worker"
)

// 设置路由
func SetRouter() easy.RouterMap {
	router := easy.RouterMap{}
	router["/rest/2.0/ufcagent"] = easy.GetHandle(&ufcagent.UfcagentController{})
	router["/rest/2.0/agent"] = &agent.AgentController{}
	router["/watchdog"] = &watchdog.WatchdogController{}
	router["/watchdog/nginxlog"] = &nginxlog.NginxlogController{}
	return router
}

// 设置定时任务路由
func SetSchedulerRouter() easy.RouterMap {
	router := easy.RouterMap{}
	router["migrate"] = &worker.MigrateScheduler{}
	router["sendMail"] = &worker.SendMailScheduler{}
	router["hiWarning"] = &worker.HiWarningScheduler{}
	router["ufcMetaRightCheck"] = &worker.UfcMetaRightCheckScheduler{}
	router["AgentLatency"] = &worker.AgentLatencyScheduler{}
	router["WatchDog"] = &worker.WatchDogScheduler{}
	router["UfcAgentLatency"] = &worker.UfcAgentLatencyScheduler{}
	router["WatchDogHi"] = &worker.WatchDogHiScheduler{}
	router["smallFlowCheck"] = &worker.SmallFlowCheckScheduler{}
	router["SiaLog"] = &worker.SiaLogScheduler{}
	return router
}

1. 背景
1.1 具体背景
UFC配置更新时间间隔过长，会导致线上出现访问到不可用的实例上，从而出现可用性问题。为了应对该问题，我们需要梳理会导致配置更新时间间隔过长的问题，并提出相应的解决方案。此外，我们还要感知到线上系统的配置更新延迟时间，因此需要收集相关信息。
1.2 配置更新问题梳理
1）上报数据的IP，可能不出现在 UFC BNS IP列表里面；UFC BNS IP 列表，可能有机器没有上报。该场景下可能有什么异常？如何处理？
2）BNS感知到 IP 变更的时间，到 ufc-meta 把新的 IP 列表落到 Redis 中，时长如何优化？
3）特权进程更新配置延迟问题，如何监控和异常检测？
4）特权进程的配置，更新到 worker 进程的延迟，如何监控和异常检测？
5）特权进程异常/挂了/hang住，如何检测和上报？
2. 目标
 完成配置更新延迟监控以应对上述问题，同时完成对异常的报警。
3. 设计思路与折衷
3.1 问题应对
为了应对上述提到的几个问题，我们在UfcAgent收集几个数据上传至服务端 UfcAdminEasy：
1）privileged 进程更新配置的延迟时间 privileged_latency，该值为privileged 进程更新mtime的时间 - redis更新的 mtime 。
2）worker 进程从 privileged 进程更新配置的延迟时间 worker_latency， 该值为 worker 进程更新mtime的时间 - privileged 进程更新mtime的时间 
3）privileged 进程当前的 mtime。

其中，
 privileged_latency 用于解决问题 3 特权进程更新配置延迟问题，如何监控和异常检测？
worker_latency 用于解决问题 4 特权进程的配置，更新到 worker 进程的延迟，如何监控和异常检测？
mtime 用于维护问题 1 提到的几个特殊 IP 集合 ，供后续服务端定时任务去介入处理可能出现的异常和 问题5 
针对问题 2 放到后续的 ufc-meta 分片需求去做，本文档中不展开讨论。具体每个问题的细节讨论，在下方展开。

主要流程图如下：
[流程图]

具体每个问题处理细节，见下方。
问题1） 上报数据的IP，可能不出现在 UFC BNS IP列表里面；UFC BNS IP 列表，可能有机器没有上报。该场景下可能有什么异常？如何处理？
记集合 A = {ip | ip 为给 UfcAdminEasy 上报数据的IP}， 集合B =  {ip | 在 ufc bns ip 列表的 ip}
A U B = （A-B） U (B-A) U (A 交 B)。
其中， A - B 表示由在 A 集合中，但不在 B 集合中的元素，构成的集合。上述右边三个集合没有交集。
对于既不在A也不在B，但是也部署 UFC 的机器（比如业务阡陌机但异常），我们不予讨论，因为我们感知不到机器存在。这时候需要根据业务反馈，再介入人工处理。


A - B: 这部分 IP 为有上报数据的 IP，但不在所属 BNS 中。
可能出现的情况为：
离线机器（阡陌机等情况），可能没有挂载。需要人工接入处理。
处理手段：
判断不在所属 BNS 后，一定是线上版本UFC传过来的请求（对于沙盒 ufc 上报的数据，默认上报到沙盒 UfcAdminEasy， 我们可通过配置控制不处理）。走以下逻辑：
    1. 检查该 IP 是否属于白名单的WhiteList HSet中（即为了避免 有上报数据的 IP，但不在所属 BNS 中情况时没有必要的 Hi 报警，我们可以搞个 HSet 手动存个白名单，如果是这些机器，则不用 HI 报警），如果不属于白名单的HSet中，
        1. 先放到报警的 HSet 中供后续定时任务进行处理。
        2. 定时任务处理逻辑如下：发Hi报警以人工介入，清除报警 HSet 的IP（可考虑 1h 执行 1 次） 


  b. 把该请求传过来的延迟信息也上报到问题提到的各个ZSet，以供后续统计和报警。

B - A: 这部分 IP 为在所属 BNS 中，但没有收到数据上报。
可能出现的情况为：
机器挂了，机器没有部署 UFC or UFC 没有启动，机器启动 UFC 但是数据上报失败（可能是配置更新失败导致，上报数据通过 ufc 拿 UfcAdminEasy IP 列表）
处理手段：
定时任务扫描 B - A 集合的IP（后面会展开如何获取 B - A） 集合，针对其中每个 IP执行以下逻辑：
1 发起http请求获取远端的 mtime
curl "ip:8240/bypass?cmd=debug&key=static_cache^mtime"
1.1 状态码非200返回（connection refused/connection timeout， 404 等情形）
此时表明机器挂了 or UFC未启动 or UFC端口号被占用。可用 get_service_by_host 获取该机器上是否含有带有 orp 和 opera 的实例，如果带有则批量 Hi 报警，人工接入处理；如果机器上面不带有 orp 实例 opera 实例，则打日志记录即可。
1.2 状态码200 返回（能成功请求) 
Hi 报警人工介入处理。（看看在 bns 列表里面，且请求成功返回，但是不上报数据）

A 交 B:    
和 A- B 走后续的正常逻辑（即后续问题3，4，5的处理）。上述 B - A 不走后续问题3，问题4，问题5的应对处理（因为没上报无法进行相应的处理）。

问题1.1 为应对问题1，我们需要维护好A 和 B 集合。如何维护？
每个数据上报过来，都需要判断该 ip 是否属于 A - B，还是 A 交 B (A 是上报的 IP 列表， B 是 UFC BNS IP 列表）。
因此我们需要维护好 UFC BNS IP 列表。由于请求上报的频率过高，每次都通过接口获取 BNS 对应的 IP 列表（开销可能过大，几万个 IP)，为了避免QPS降低， 这里通过本地内存去维护该列表：
1） 服务启动时先初始化该列表。
2） 定时任务定时更新(每 30min) 去更新缓存的 BNS IP 列表数据。
对于该缓存的使用和初始化，有以下注意事项：
1）每次对内存的访问都加锁处理；
2）每次查询如果发现内存里面没 ready 好则重新初始化， 初始化过程中通过全局原子变量标记是否正在更新。
3）如果请求过来时，缓存数据正在更新（通过全局原子变量检测），则认为该 IP 在 BNS 列表里面，继续走数据上报逻辑。

我们还需要维护好集合 A 的列表(有些定时任务需要）。这里遍历问题 3 中提到的 mtime 保存的 ZSet 即可（定时任务不频繁，遍历几万个key可以接受）

问题2） BNS感知到 IP 变更的时间，到 ufc-meta 把新的 IP 列表落到 Redis 中，时长如何优化？
我们从外部视角，不能获知到 BNS 中 IP 变化的时间，只能最大化地缩小 ufc-meta 的一轮配置更新时间间隔，该问题放到后面的 ufc-meta 分片更新需求中去做，这里不予展开。
问题3）特权进程更新配置延迟问题，如何监控和异常检测？
UfcAgent侧：
为了检测特权进程配置更新延迟问题，可以上报值：
privilege_latency  = timeNow - redisMtime
字段
备注
timeNow
特权进程更新完一轮配置时的unix时间戳
redisMtime
ufc-meta更新完一轮配置往 redis写的全局mtime
该值的含义为特权进程更新配置的延迟。

UfcAdminEasy侧：
将数据存储到 ZsetA供统计和分析，以及超过阈值时（5min）做相应的报警处理（ Hi报警 & 日志）
redis具体参数
参数值
备注
key 

固定 key，不按时间拆分，可重复覆盖。
field
ip
机器 ip
score
worker_latency
耗时信息
问题4） 特权进程的配置，更新到 worker 进程的延迟，如何监控和异常检测？
UfcAgent 侧：
UfcAdmin 侧需要上报一个值:  worker_latency =  max(worker_latency, PrivilegedTimeNow - WorkerTimeNow)
字段
备注
worker_latency
max (后两者的差值)，含义为 worker 进程更新的耗时信息，初始化为 -1， 每轮上传后重置为 -1。
这里取 max, 是因为有多个 worker 进程，我们只关注最慢更新的 worker 进程延迟即可。
PrivilegedTimeNow
特权进程更新完一轮配置时的unix时间戳
WorkerTimeNow
Worker进程更新完一轮配置时的 unix 时间戳
UfcAdminEasy侧：
1）收到 worker_latency，判断是否为 -1
1.1） 如果 worker_latency 值为 -1， 则报警：打日志 & 上报 ZSet B缓存 &  Hi 报警。
（这是因为 worker_latency的延迟时间预期不会超过 25s, UFC特权进程上报时间延迟信息设置为 1 分钟，这表明，在任意一个 1 min 时间间隔内，都会有 worker 进程更新 worker_latency 该值）。
return 
1.2） 如果 worker_latency 不为 -1： 
1.2.1 则上报 Zset B缓存，用于后续的分析。
1.2.2 如果该值超过阈值 25s，报警：打日志 & 上报 ZSet B缓存 &  Hi 报警。

存储到 ZsetB 的数据如下：
redis具体参数
参数值
备注
key 

固定 key，不按时间拆分，可重复覆盖。
field
ip

score
worker_latency
耗时信息。
问题5） 特权进程异常/挂了/hang住，如何检测和上报？
UfcAgent 侧: 
分为有数据上报和无数据上报两种情况：
无数据上报（比如 hang住/挂了）：属于在 B - A 列表中（在 BNS IP 列表里但没有数据上报）
上报字段 PrivilegedRedisMtime （UFC 特权进程中最新的全局 mtime) ，该字段初始化为 -1。
字段为 -1 可能为以下情况：1） 配置更新失败/访问Redis失败，2） 机器重启时前几分钟特权进程还未来得及更新该值。

UfcAdminEasy侧：
检测 PrivilegedRedisMtime 字段是否为 -1
当字段为 -1 时：
只有情况1）为异常情况。我们的处理手段为先把该 ip 放到一个 HSet 中，后续起定时任务去遍历扫描 HSet 的 value TimeNow 离当前TimeNow 的时间差，超过 5 min Hi 报警，定时任务时间间隔可以先 5 min 执行一遍，扫完后删除该 Key（如果一直失败，后续依然后上传值为 -1， 会重复触发 Hi 报警）
字段名称
值
含义
HSet  Key

该HSet, 收集为 mtime 为 -1 的IP
Field
IP
上报机器IP
Value
timeNow
收到该请求的 Unix时间戳
当字段 PrivilegedRedisMtime 不为 -1 时，
1） 从上述的Hset 中删去该 Key。
2） 并把  PrivilegedRedisMtime 放到 ZSet C中去缓存供后续统计。
3） 如果 PrivilegedRedisMtime 和收集到 PrivilegedRedisMtime 时的timeNow 超过阈值 5min, 则：打日志 & Hi 报警。

上报到 ZsetC的数据如下：
参数

含义
ZSetC  Key

该 ZSet 存储特权进程上报的当前mtime时间戳，供后续统计和分析。
Field
IP
上报机器IP
Value
timeNow
收到该请求的 Unix时间戳

3.2 UfcAgent侧
根据上述讨论，UfcAgent 上报时需要新增以下字段：
{
    "mtime": // 特权进程的当前 mtime,即上述讨论中的 PrivilegedRedisMtime
    "worker_elaspe": // privilege 进程更新完 mtime 的 timeNow，到 worker进程更新完 mtime时的 timeNow 的差值
    "privilege_elapse": // redis 全局 mtime 和 privilege 进程更新完 mtime 时的 差值
}
除了3.1 的问题应对外，需要优化监控上报逻辑，将 worker 上报逻辑改到 privileged 特权进程上报。通过特权进程进行交互，代码细节这里不予展开。

3.3 UfcAdminEasy侧
根据 3.1 的问题应对来完成相关开发，无特殊注意事项。

4. 潜在风险
暂无潜在的风险。














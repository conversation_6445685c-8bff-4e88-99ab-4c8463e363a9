# 默认使用环境变量中的 GOROOT 路径，线上 Agile 使用 EE 预装的最新的 Golang 版本，如需其它版本，请自行设置
GOROOT := $(shell go env GOROOT)

#CONF_PATH=$(OUTDIR)/conf

# PROJECT 默认为用户的项目名字，产出的 SO 文件默认为 $(HOMEDIR)/bin/<PROJECT>.so
PROJECT := UfcAdminEasy
PROJECT_BINARY_NAME:=$(PROJECT).so
PROJECT_BINARY_PATH:=bin/$(PROJECT_BINARY_NAME)

# PROJECT_BINARY_TARGET 设置 SO 文件的编译参数和编译路径
# 如果用户在编译 SO 文件时需要设置特殊的参数，可以在这里填写
PROJECT_BINARY_TARGET := -o $(PROJECT_BINARY_PATH)
# PROJECT_BINARY_TARGET_TWO := -o $(PROJECT_BINARY_PATH_TWO)

# TARGET 是 Makefile 中默认用于定义编译产出文件的字段，在这里配置需要编译的目标
# 支持多个 TARGET 每个 TARGET 定义一个 SO 的输出
# 多个 TARGET 的设置方式
# TARGET := PROJECT_BINARY PROJECT_BINARY_TARGET_TWO
TARGET := PROJECT_BINARY_TARGET

# prepare 设置 CI 时在编译前执行的命令
prepare:
	@echo "can do something before build"
	@rm -rf output

# debug-prepare 设置 make debug 运行时先执行的命令
debug-prepare:
	@echo "can do something before debug build"

# release 设置 CI 流程的产出文件，需要把线上使用到的文件都拷贝到 output 文件夹中
#
# 所有配置在上线时从云端自动拉取，禁止将【配置文件】拷贝至产出目录，【后果自负】！！！
#
# 默认的 $(OUTDIR) 为项目根目录下的 output，只需要移动文件，tar 操作不需要用户完成
release:
	@echo "pre release move file to $(OUTDIR) dir"
	@mkdir -p $(OUTDIR)/bin

	mv $(PROJECT_BINARY_PATH) $(OUTDIR)/bin/

	chmod +x noah_control
	cp noah_control $(OUTDIR)/bin/

	@echo "copy noahdes for archer3 proto"
	cp -r noahdes $(OUTDIR)/

# 自定义清理任务，默认已清理 ${OUTDIR} 、${HOMEDIR}/bin、${HOMEDIR}/log 三个目录
custom_clean:
	@echo "can do something before clean"
# 	rm -rf test2.txt
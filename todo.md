1. 背景
1.1 具体背景
在现有工作中，我们已经完成了对各个机器（包括异常机器）的延迟数据收集，并能够感知配置更新延迟及相关异常。具体见：端到端配置更新延迟监控设计2.0
目前，我们希望基于已有的收集能力，进一步实现以下两类功能：
1. 监控面板机房粒度展示

    * 在现有监控面板中，延迟数据按单机维度展示不利于运维人员快速定位问题，需要将数据聚合到机房粒度，并提供多机房对比能力。

2. 延迟百分位统计展示

    * 对延迟数据进行 p99 / p95 / p90 / p80 百分位统计，支持机房粒度和全局粒度（总粒度），并在监控面板上展示。

这些功能便于观察延迟分布趋势，为后续的延迟优化提供数据支持。

2. 目标
* 将现有延迟数据采集体系扩展为 支持机房粒度聚合展示
* 增加 延迟数据的 p99 / p95 / p80 计算与展示，支持机房粒度与全局粒度
* 保留现有单机延迟监控、异常报警能力（Hi报警）


3. 设计思路与折衷
3.1 功能拆解
（1）机房粒度展示
* 需求：
    * 在监控面板上以机房为单位展示延迟数据（包括 privileged_latency / worker_latency， privileged_latency + worker_latency， 等），并可切换查看单机数据
    * 支持多机房展示，以便对比不同机房的性能差异

* 实现思路：

    1. 机房维度标识

        * 在 UfcAgent 上报数据时，可通过 IP 获取 idc 字段（可通过 ip 获得 hostname ）

    2. 存储与聚合

        * 延迟数据在 Redis ZSet 中按 idc 对应的 ufc 侧逻辑机房（beijing, yangquan, xian) 分桶。
        * 同时保留全局（不分机房）的 ZSet，用于全局粒度计算

    3. 展示

        * 打日志并配置日志采集，采集展示到 SIA 面板上。


（2）延迟百分位计算（p99 / p95 / p90 / p80）
* 需求：
    * 对延迟数据计算 p99、p95、p90、p80
    * 支持机房维度和全局维度

* 实现思路：

    1. 数据存储

        * 延迟数据存储到 Redis ZSet，score 为延迟值，member 为 ip（或 ip:timestamp）
        * 按 metric（privileged_latency / worker_latency 等）+ 机房进行分桶

    2. 百分位计算

        * 定时任务扫描 ZSet，根据窗口期数据量和百分位计算公式，得到 p99、p95、p90、p80
        * 计算逻辑：
            * N = 数据总量（ 对于总 redis的话是总机器数；对于分机房情形，是机房的机器数）
            * p99 对应第 ceil(0.99 * N) 个延迟值
            * p95 对应第 ceil(0.95 * N) 个延迟值
            * p90 对应第 ceil(0.90 * N) 个延迟值
            * p80 对应第 ceil(0.80 * N) 个延迟值



（3）与现有监控的兼容性
* 原有延迟监控超过阈值情况，上报至Hi群保持不变
* 如果 p99 超过阈值，可增加新的机房级报警规则（例如 p99 > 500ms 触发报警）


3.2 UfcAgent 侧改造
无需改造。

3.3 UfcAdminEasy 侧改造
1. 数据存储

    * 新增按机房维度（机房为ufc侧逻辑机房，如 beijing，xian，yangquan） 存储延迟数据：

2. 定时统计

    * 新增一个 X min定时把相关数据打出到日志，数据如下：
        * worker进程从 privileged 进程更新延迟数据（即 worker_latency) 的 P99, P95，P90, P80。
        * worker进程从 privileged 进程更新延迟数据（即 worker_latency) 超过一定阈值（如 60s) 的机器数量。
        * 特权进程更新数据时从 redis 更新数据 mtime (即 privileged_latency) 的 P99, P95，P90, P80。
        * 特权进程更新数据时从 redis 更新数据 mtime (即 privileged_latency) 超过一定阈值（如 60s)的机器数量。
        * ufc-agent 的 mtime 到执行统计时timestamp 的 diff 的  P99, P95，P90, P80。
        * ufc-agent 的 mtime 到执行统计时timestamp，超过一定阈值（如 300s) 的机器数量。

    * 定时器时间可配置，暂定为 1 min（不能超过 5 min）， 由于机器量大上述定时器执行耗时可能较长，1min 未必能执行完，到时候根据具体情况再作调整。


4. 潜在风险
* 机房粒度聚合可能会造成 Redis key 数量增加，需要评估内存开销。
* 百分位计算频率过高可能带来额外 CPU 消耗，需根据上线情况 CPU 是否过高考虑调整频率。
package worker

import (
	"math"
	"sort"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
)

// LatencyStats 延迟统计结构
type LatencyStats struct {
	IDC                  string  `json:"idc"`                    // 机房名称，"global" 表示全局
	MetricType           string  `json:"metric_type"`            // 指标类型：worker_latency, privileged_latency, agent_latency
	P100                 float64 `json:"p100"`                   // P100 百分位（最大值）
	P99                  float64 `json:"p99"`                    // P99 百分位
	P95                  float64 `json:"p95"`                    // P95 百分位
	P90                  float64 `json:"p90"`                    // P90 百分位
	P80                  float64 `json:"p80"`                    // P80 百分位
	TotalCount           int     `json:"total_count"`            // 总机器数
	ExceedThresholdCount int     `json:"exceed_threshold_count"` // 超过阈值的机器数
	ThresholdValue       int64   `json:"threshold_value"`        // 阈值
	Timestamp            int64   `json:"timestamp"`              // 统计时间戳
}

// 定时任务处理的handler
func (s *SiaLogScheduler) Handler() {
	ctx := easy.NewContext()
	ctx.SLog.Info("SiaLogScheduler handler start").Print()

	redis := easy.NewRedis(ctx, "redis")
	timestamp := time.Now().Unix()

	// 机房列表
	idcs := []string{"beijing", "yangquan", "xian"}

	// 统计全局数据
	s.processAllStats(ctx, redis, timestamp)

	// 统计各机房数据
	for _, idc := range idcs {
		s.processIDCStats(ctx, redis, idc, timestamp)
	}

	ctx.SLog.Info("SiaLogScheduler handler completed").Print()
}

// processGlobalStats 处理全局统计
func (s *SiaLogScheduler) processAllStats(ctx *easy.Context, redis *easy.Redis, timestamp int64) {
	// Worker 延迟统计
	workerStats := s.calculateLatencyStats(ctx, redis, "all", "worker_latency", cacheutil.GetUFCWorkerLatencyZSetName(), 60)
	if workerStats != nil {
		workerStats.Timestamp = timestamp
		ctx.SLog.Info("all worker latency stats").
			Set("idc", workerStats.IDC).
			Set("metric_type", workerStats.MetricType).
			Set("p100", workerStats.P100).
			Set("p99", workerStats.P99).
			Set("p95", workerStats.P95).
			Set("p90", workerStats.P90).
			Set("p80", workerStats.P80).
			Set("total_count", workerStats.TotalCount).
			Set("exceed_threshold_count", workerStats.ExceedThresholdCount).
			Set("threshold_value", workerStats.ThresholdValue).
			Set("timestamp", workerStats.Timestamp).Print()
	}

	// Privileged 延迟统计
	privilegedStats := s.calculateLatencyStats(ctx, redis, "all", "privileged_latency", cacheutil.GetUFCPrivilegedLatencyZSetName(), 60)
	if privilegedStats != nil {
		privilegedStats.Timestamp = timestamp

		ctx.SLog.Info("all privileged latency stats").
			Set("idc", privilegedStats.IDC).
			Set("metric_type", privilegedStats.MetricType).
			Set("p100", privilegedStats.P100).
			Set("p99", privilegedStats.P99).
			Set("p95", privilegedStats.P95).
			Set("p90", privilegedStats.P90).
			Set("p80", privilegedStats.P80).
			Set("total_count", privilegedStats.TotalCount).
			Set("exceed_threshold_count", privilegedStats.ExceedThresholdCount).
			Set("threshold_value", privilegedStats.ThresholdValue).
			Set("timestamp", privilegedStats.Timestamp).Print()
	}

	// Total 延迟统计（worker_latency + privileged_latency）
	totalStats := s.calculateLatencyStats(ctx, redis, "all", "total_latency", cacheutil.GetUFCTotalLatencyZSetName(), 120)
	if totalStats != nil {
		totalStats.Timestamp = timestamp

		ctx.SLog.Info("all total latency stats").
			Set("idc", totalStats.IDC).
			Set("metric_type", totalStats.MetricType).
			Set("p100", totalStats.P100).
			Set("p99", totalStats.P99).
			Set("p95", totalStats.P95).
			Set("p90", totalStats.P90).
			Set("p80", totalStats.P80).
			Set("total_count", totalStats.TotalCount).
			Set("exceed_threshold_count", totalStats.ExceedThresholdCount).
			Set("threshold_value", totalStats.ThresholdValue).
			Set("timestamp", totalStats.Timestamp).Print()
	}

	// Agent 延迟统计（mtime 到当前时间的差值）
	agentStats := s.calculateAgentLatencyStats(ctx, redis, "all", cacheutil.GetUFCIPMtimesZSetName(), 300, timestamp)
	if agentStats != nil {
		agentStats.Timestamp = timestamp
		ctx.SLog.Info("all agent latency stats").
			Set("idc", agentStats.IDC).
			Set("metric_type", agentStats.MetricType).
			Set("p100", agentStats.P100).
			Set("p99", agentStats.P99).
			Set("p95", agentStats.P95).
			Set("p90", agentStats.P90).
			Set("p80", agentStats.P80).
			Set("total_count", agentStats.TotalCount).
			Set("exceed_threshold_count", agentStats.ExceedThresholdCount).
			Set("threshold_value", agentStats.ThresholdValue).
			Set("timestamp", agentStats.Timestamp).Print()
	}
}

// processIDCStats 处理机房统计
func (s *SiaLogScheduler) processIDCStats(ctx *easy.Context, redis *easy.Redis, idc string, timestamp int64) {
	// Worker 延迟统计
	workerStats := s.calculateLatencyStats(ctx, redis, idc, "worker_latency", cacheutil.GetUFCWorkerLatencyZSetNameByIDC(idc), 60)
	if workerStats != nil {
		workerStats.Timestamp = timestamp
		ctx.SLog.Info("idc worker latency stats").
			Set("idc", workerStats.IDC).
			Set("metric_type", workerStats.MetricType).
			Set("p100", workerStats.P100).
			Set("p99", workerStats.P99).
			Set("p95", workerStats.P95).
			Set("p90", workerStats.P90).
			Set("p80", workerStats.P80).
			Set("total_count", workerStats.TotalCount).
			Set("exceed_threshold_count", workerStats.ExceedThresholdCount).
			Set("threshold_value", workerStats.ThresholdValue).
			Set("timestamp", workerStats.Timestamp).Print()
	}

	// Privileged 延迟统计
	privilegedStats := s.calculateLatencyStats(ctx, redis, idc, "privileged_latency", cacheutil.GetUFCPrivilegedLatencyZSetNameByIDC(idc), 60)
	if privilegedStats != nil {
		privilegedStats.Timestamp = timestamp
		ctx.SLog.Info("idc privileged latency stats").
			Set("idc", privilegedStats.IDC).
			Set("metric_type", privilegedStats.MetricType).
			Set("p100", privilegedStats.P100).
			Set("p99", privilegedStats.P99).
			Set("p95", privilegedStats.P95).
			Set("p90", privilegedStats.P90).
			Set("p80", privilegedStats.P80).
			Set("total_count", privilegedStats.TotalCount).
			Set("exceed_threshold_count", privilegedStats.ExceedThresholdCount).
			Set("threshold_value", privilegedStats.ThresholdValue).
			Set("timestamp", privilegedStats.Timestamp).Print()
	}

	// Total 延迟统计（worker_latency + privileged_latency）
	totalStats := s.calculateLatencyStats(ctx, redis, idc, "total_latency", cacheutil.GetUFCTotalLatencyZSetNameByIDC(idc), 120)
	if totalStats != nil {
		totalStats.Timestamp = timestamp
		ctx.SLog.Info("idc total latency stats").
			Set("idc", totalStats.IDC).
			Set("metric_type", totalStats.MetricType).
			Set("p100", totalStats.P100).
			Set("p99", totalStats.P99).
			Set("p95", totalStats.P95).
			Set("p90", totalStats.P90).
			Set("p80", totalStats.P80).
			Set("total_count", totalStats.TotalCount).
			Set("exceed_threshold_count", totalStats.ExceedThresholdCount).
			Set("threshold_value", totalStats.ThresholdValue).
			Set("timestamp", totalStats.Timestamp).Print()
	}

	// Agent 延迟统计
	agentStats := s.calculateAgentLatencyStats(ctx, redis, idc, cacheutil.GetUFCIPMtimesZSetNameByIDC(idc), 300, timestamp)
	if agentStats != nil {
		agentStats.Timestamp = timestamp
		ctx.SLog.Info("idc agent latency stats").
			Set("idc", agentStats.IDC).
			Set("metric_type", agentStats.MetricType).
			Set("p100", agentStats.P100).
			Set("p99", agentStats.P99).
			Set("p95", agentStats.P95).
			Set("p90", agentStats.P90).
			Set("p80", agentStats.P80).
			Set("total_count", agentStats.TotalCount).
			Set("exceed_threshold_count", agentStats.ExceedThresholdCount).
			Set("threshold_value", agentStats.ThresholdValue).
			Set("timestamp", agentStats.Timestamp).Print()
	}
}

// calculateLatencyStats 计算延迟统计
func (s *SiaLogScheduler) calculateLatencyStats(ctx *easy.Context, redis *easy.Redis, idc, metricType, zsetKey string, threshold int64) *LatencyStats {
	// 获取 ZSet 总数量
	totalCount, err := redis.ZCard(zsetKey)
	if err != nil {
		ctx.SLog.Warning("redis zcard error").Set("zset", zsetKey).SetErr(err).Print()
		return nil
	}

	if totalCount == 0 {
		ctx.SLog.Info("no data in zset").Set("zset", zsetKey).Print()
		return nil
	}

	// 计算百分位对应的索引（ZSet 是按分数升序排列的）
	p80Index := int64(math.Ceil(0.80*float64(totalCount))) - 1
	p90Index := int64(math.Ceil(0.90*float64(totalCount))) - 1
	p95Index := int64(math.Ceil(0.95*float64(totalCount))) - 1
	p99Index := int64(math.Ceil(0.99*float64(totalCount))) - 1
	p100Index := int64(totalCount) - 1 // P100 是最大值，即最后一个元素

	// 确保索引不超出范围
	totalCountInt64 := int64(totalCount)
	if p80Index < 0 {
		p80Index = 0
	}
	if p90Index < 0 {
		p90Index = 0
	}
	if p95Index < 0 {
		p95Index = 0
	}
	if p99Index < 0 {
		p99Index = 0
	}
	if p100Index < 0 {
		p100Index = 0
	}
	if p80Index >= totalCountInt64 {
		p80Index = totalCountInt64 - 1
	}
	if p90Index >= totalCountInt64 {
		p90Index = totalCountInt64 - 1
	}
	if p95Index >= totalCountInt64 {
		p95Index = totalCountInt64 - 1
	}
	if p99Index >= totalCountInt64 {
		p99Index = totalCountInt64 - 1
	}
	if p100Index >= totalCountInt64 {
		p100Index = totalCountInt64 - 1
	}

	// 计算超过阈值的数量（通过获取所有数据来计算）
	exceedCount := s.countExceedThreshold(ctx, redis, zsetKey, threshold)

	stats := &LatencyStats{
		IDC:                  idc,
		MetricType:           metricType,
		TotalCount:           totalCount,
		ExceedThresholdCount: exceedCount,
		ThresholdValue:       threshold,
	}

	// 直接从 Redis 获取百分位数值
	stats.P80 = s.getPercentileFromRedis(ctx, redis, zsetKey, p80Index)
	stats.P90 = s.getPercentileFromRedis(ctx, redis, zsetKey, p90Index)
	stats.P95 = s.getPercentileFromRedis(ctx, redis, zsetKey, p95Index)
	stats.P99 = s.getPercentileFromRedis(ctx, redis, zsetKey, p99Index)
	stats.P100 = s.getPercentileFromRedis(ctx, redis, zsetKey, p100Index)

	return stats
}

// calculateAgentLatencyStats 计算 Agent 延迟统计（mtime 到当前时间的差值）
func (s *SiaLogScheduler) calculateAgentLatencyStats(ctx *easy.Context, redis *easy.Redis, idc, zsetKey string, threshold int64, currentTime int64) *LatencyStats {
	// 获取 ZSet 中的所有数据
	slice, err := redis.ZRevRangeWithScores(zsetKey, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error").Set("zset", zsetKey).SetErr(err).Print()
		return nil
	}

	if len(slice) == 0 {
		ctx.SLog.Info("no data in zset").Set("zset", zsetKey).Print()
		return nil
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange with scores failed").Set("zset", zsetKey).Set("len", len(slice)).Print()
		return nil
	}

	// 解析数据，计算延迟
	var latencies []float64
	exceedCount := 0
	totalCount := len(slice) / 2

	for i := 0; i < totalCount; i++ {
		mtime := conv.ToInt64(slice[2*i+1])
		if mtime <= 0 {
			// 跳过无效的 mtime
			continue
		}

		latency := currentTime - mtime
		if latency < 0 {
			latency = 0
		}

		latencies = append(latencies, float64(latency))

		if latency > threshold {
			exceedCount++
		}
	}

	if len(latencies) == 0 {
		return nil
	}

	// 排序
	sort.Float64s(latencies)

	// 计算百分位
	stats := &LatencyStats{
		IDC:                  idc,
		MetricType:           "agent_latency",
		TotalCount:           len(latencies),
		ExceedThresholdCount: exceedCount,
		ThresholdValue:       threshold,
	}

	stats.P80 = calculatePercentile(latencies, 0.80)
	stats.P90 = calculatePercentile(latencies, 0.90)
	stats.P95 = calculatePercentile(latencies, 0.95)
	stats.P99 = calculatePercentile(latencies, 0.99)
	stats.P100 = calculatePercentile(latencies, 1.0) // P100 是最大值

	return stats
}

// calculatePercentile 计算百分位数
func calculatePercentile(sortedData []float64, percentile float64) float64 {
	if len(sortedData) == 0 {
		return 0
	}

	if len(sortedData) == 1 {
		return sortedData[0]
	}

	// 计算索引位置
	index := percentile * float64(len(sortedData)-1)
	lower := int(math.Floor(index))
	upper := int(math.Ceil(index))

	if upper >= len(sortedData) {
		upper = len(sortedData) - 1
	}
	if lower < 0 {
		lower = 0
	}

	if lower == upper {
		return sortedData[lower]
	}

	// 线性插值
	weight := index - float64(lower)
	return sortedData[lower]*(1-weight) + sortedData[upper]*weight
}

// getPercentileFromRedis 从 Redis ZSet 中获取指定索引位置的分数值
func (s *SiaLogScheduler) getPercentileFromRedis(ctx *easy.Context, redis *easy.Redis, zsetKey string, index int64) float64 {
	// 使用 ZRANGE 获取指定索引位置的元素和分数
	slice, err := redis.ZRangeWithScores(zsetKey, int(index), int(index))
	if err != nil {
		ctx.SLog.Warning("redis zrange with scores error").Set("zset", zsetKey).Set("index", index).SetErr(err).Print()
		return 0
	}

	if len(slice) < 2 {
		ctx.SLog.Warning("redis zrange with scores no data").Set("zset", zsetKey).Set("index", index).Print()
		return 0
	}

	// slice[1] 是分数值
	return conv.ToFloat64(slice[1])
}

// countExceedThreshold 计算超过阈值的数量
func (s *SiaLogScheduler) countExceedThreshold(ctx *easy.Context, redis *easy.Redis, zsetKey string, threshold int64) int {
	// 获取所有分数值
	slice, err := redis.ZRevRangeWithScores(zsetKey, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error for count exceed").Set("zset", zsetKey).SetErr(err).Print()
		return 0
	}

	if len(slice) == 0 {
		return 0
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange with scores failed for count exceed").Set("zset", zsetKey).Set("len", len(slice)).Print()
		return 0
	}

	exceedCount := 0
	totalCount := len(slice) / 2

	for i := 0; i < totalCount; i++ {
		latency := conv.ToInt64(slice[2*i+1])
		if latency > threshold {
			exceedCount++
		}
	}

	return exceedCount
}

package worker

import (
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

// 定时任务处理的handler
func (s *UfcMetaRightCheckScheduler) Handler() {
	ctx := easy.NewContext()
	// 结构化日志打印，详情可参见https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	ctx.SLog.Info("--------handler").Print()

	prefixSlice := conf.Application.UfcKeyRightCheckPrefix

	timeNowUnix := time.Now().Unix()

	redis := easy.NewRedis(ctx, "redis")

	var mtimeKeys []string
	for _, prefix := range prefixSlice {
		mtimeKey := fmt.Sprintf("%s-mtime", prefix)
		mtime, err := redis.Get(mtimeKey)

		if err != nil {
			ctx.SLog.Error("get mtime error").Set("prefix", prefix).SetErr(err).Print()
			continue
		}

		mtimeInt := conv.ToInt64(mtime)
		if mtimeInt == 0 {
			ctx.SLog.Error("convert mtime to int error").Set("prefix", prefix).Set("mtime", mtime).SetErr(err).Print()
			continue
		}
		latency := timeNowUnix - mtimeInt
		ctx.SLog.Error("redis mtime latency info").Set("prefix", prefix).Set("mtime", mtime).Set("latency", latency).SetErr(err).Print()

		// 延迟 > 30 min 时报警
		if latency > 60*60 {
			ctx.SLog.Warning("mtime is too old").Set("prefix", prefix).Print()
			mtimeKeys = append(mtimeKeys, mtimeKey)
		}
	}

	if len(mtimeKeys) == 0 {
		return
	}

	mtimeKeysByte, err := json.Marshal(mtimeKeys)
	if err != nil {
		ctx.SLog.Error("marshal mtimeKeys error").SetErr(err).Print()
		return
	}

	text := fmt.Sprintf("UfcMetaRightCheckScheduler alarm: mtime is too old\n\n %s", string(mtimeKeysByte))

	option := common.SendHiOption{
		Type:    common.SendHiTypeText,
		Content: text,
	}
	if err := common.SendHi(conf.Application.HiWarning.OtherURL, option); err != nil {
		ctx.SLog.Error("send hi error").SetErr(err).Print()
		return
	}
}

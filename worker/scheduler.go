/*
* Easy生成，**平台修改本地update不会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: 定时任务handler
 */
package worker

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"time"

	humanize "github.com/dustin/go-humanize"
	"go.uber.org/ratelimit"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/entity/remote"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/hashutil"
)

// 可参考 opera接口文档: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Jk_2Ea1IJe/LKY992s_Ji/qyOFqz42TqsdUV

const (
	// 需要传给opera 的参数

	// 用于线下本地测试
	TestOperaUser        = "opera"
	TestOperaPassword    = "opera"
	TestUserNameForOpera = "opera"

	// 用于线上运行
	ProductlineName  = "wangpan"
	OperaUser        = "wangpan"
	OperaPassWord    = "wpjo89noJOYaIOo23p"
	UserNameForOpera = "wangpan"

	OwnerForOpera = "zhongzhenyan"
)

var (
	WhiteListBnsCache = make(map[string]struct{})
)

func loadOperaUser() string {
	return OperaUser
}

func loadOperaPassword() string {
	return OperaPassWord
}

func loadUserName() string {
	return UserNameForOpera
}

func loadOwnerForOpera() string {
	return OwnerForOpera
}

// 定时任务处理的handler
func (s *MigrateScheduler) Handler() {
	ctx := easy.NewContext()
	ctx.SLog.Info("--------handler").Print()

	redis := easy.NewRedis(ctx, "redis")

	// 获取上一次用到了哪些hashtable
	set, err := hashutil.GetLastHashSet()
	if err != nil {
		ctx.SLog.Error("get last hash set failed").SetErr(err).Print()
		return
	}

	realSetName := cacheutil.WithConfigCachePrefix(set)
	// 获取所有hashtable
	hashTableNames, err := redis.SMembers(realSetName)
	if err != nil {
		ctx.SLog.Error("get members of set failed").Set("set", realSetName).SetErr(err).Print()
		return
	}

	instancesMap, err := s.migrateInstanceSelect(ctx, redis, hashTableNames)
	if err != nil {
		ctx.SLog.Error("select migrate instances failed").Set("set", realSetName).SetErr(err).Print()
		return
	}
	instanceList := instanceMap2List(instancesMap)
	// nolint:lllSendToMailHourly
	// 把要迁移的实例落到 redis 中，缓存起来待排查问题, 这里 instance 格式为 "176.netdisk-online-netdisk-online-recent.orp.hba|10.228.62.30:2044|netdisk-recent-bj|group.opera-default-netdisk-online-netdisk-online-recent-all.wangpan.all"
	if err := CacheMigrateInstances(ctx, redis, instanceList); err != nil {
		ctx.SLog.Error("cache migrate instances failed").Set("set", realSetName).SetErr(err).Print()
		return
	}

	instanceList = getRealInstanceList(ctx, instanceList)

	maxCnt := conf.Application.Migrate.MigrationMaxCnt
	if maxCnt <= 0 {
		maxCnt = 100
	}

	instancesLen := len(instanceList)
	enabled := conf.Application.Migrate.MigrationEabled

	ctx.SLog.Info("migrate info at hourly").Set("migrate enabled", enabled).Set("max cnt", maxCnt).Set("cnt", instancesLen).Print()
	if !enabled {
		return
	}

	// 限制最大迁移个数
	if maxCnt < uint32(instancesLen) {
		instanceList = instanceList[:maxCnt]
	}

	// 限制 qps 为 5
	rl := ratelimit.New(5)
	var migrateSuccessList []string
	var migrateFailedList []string
	for _, instance := range instanceList {
		rl.Take()
		err := MigrateInstance(ctx, instance)
		if err != nil {
			ctx.SLog.Error("migrate instance failed").Set("instance", instance).SetErr(err).Print()
			migrateFailedList = append(migrateFailedList, instance)
			continue
		}

		migrateSuccessList = append(migrateSuccessList, instance)
	}

	// 把所有成功迁移的实例邮件存到 redis，并邮件告知
	sendMailWorker := NewSendMailWorker(ctx)
	if err := sendMailWorker.SendToMailHourly(migrateSuccessList, migrateFailedList); err != nil {
		ctx.SLog.Warning("send mail at hourly failed").
			Set("total len", len(migrateSuccessList)+len(migrateFailedList)).
			Set("success len", len(migrateSuccessList)).
			Set("failed len", len(migrateFailedList)).
			Set("migrate success list", migrateSuccessList).
			Set("migrate failed list", migrateFailedList).Print()
	}
	ctx.SLog.Warning("send mail at hourly success").
		Set("total len", len(migrateSuccessList)+len(migrateFailedList)).
		Set("success len", len(migrateSuccessList)).
		Set("failed len", len(migrateFailedList)).
		Set("migrate success list", migrateSuccessList).
		Set("migrate failed list", migrateFailedList).Print()
}

func instanceMap2List(instancesMap map[string]map[string]string) []string {
	var res []string
	for _, instanceMap := range instancesMap {
		for _, instanceInfo := range instanceMap {
			res = append(res, instanceInfo)
		}
	}
	return res
}

func (s *MigrateScheduler) migrateInstanceSelect(ctx *easy.Context, redis *easy.Redis, hashTableNames []string) (map[string]map[string]string, error) {
	instancesMap := make(map[string]map[string]string)
	hashTableCnt := len(hashTableNames)
	var cacheCapacity uint64 = 0
	var totalFieldCnt uint64 = 0

	start := time.Now()
	for _, hashTableName := range hashTableNames {
		resTmp, cnt, capLen, err := s.migrateInstanceSelectForHashTable(ctx, redis, hashTableName)
		if err != nil {
			ctx.SLog.Error("get instance select for hash table failed").Set("hashTableName", hashTableName).SetErr(err).Print()
			continue
		}
		cacheCapacity += capLen
		totalFieldCnt += uint64(cnt)

		for bns, subInstanceSet := range resTmp {
			intanceSet, ok := instancesMap[bns]
			if !ok {
				intanceSet = map[string]string{}
			}

			for instanceID, instanceInfo := range subInstanceSet {
				intanceSet[instanceID] = instanceInfo
			}
			instancesMap[bns] = intanceSet
		}
	}
	elapse := time.Since(start).String()

	totalCap := humanize.Bytes(cacheCapacity)
	// nolint:lll
	ctx.SLog.Info("select migrate instance").Set("hashtable cnt", hashTableCnt).Set("cost", elapse).Set("filedcnt", totalFieldCnt).Set("totalcap", totalCap).Print()

	whiteListApp, err := loadAndUpdateCacheWhiteListApp(ctx, redis)
	if err != nil {
		ctx.SLog.Warning("load and update cache white list app failed").SetErr(err).Print()
		return nil, err
	}

	return instancesFilter(ctx, whiteListApp, instancesMap), nil
}

// nolint:lll
// 迁移实例挑选
func (s *MigrateScheduler) migrateInstanceSelectForHashTable(ctx *easy.Context, redis *easy.Redis, realHashtableName string) (map[string]map[string]string, int, uint64, error) {
	forbidInfo, err := redis.HgetallReturnMap(realHashtableName)
	if err != nil {
		ctx.SLog.Warning("get forbidInfo failed").Set("hashtable", realHashtableName).SetErr(err).Print()
		return nil, 0, 0, err
	}

	// 记录对应hashtable的容量，用于预估占用的 redis 内存大小，以便调整hashtable 个数以及策略
	hashTableLen := len(forbidInfo)
	var hashTableCap uint64 = 0

	res := make(map[string]map[string]string)
	// 该hash表下符合要求的节点信息
	for instanceKey, instanceInfo := range forbidInfo {
		hashTableCap += uint64(len(instanceKey))
		hashTableCap += uint64(len(instanceInfo))
		// instanceName|backend|serviceName|bns
		strTmp := strings.Split(instanceKey, "|")
		if len(strTmp) != 4 {
			ctx.SLog.Warning("instance format error").Set("instance", instanceKey).Set("info", instanceInfo).Print()
			continue
		}
		instanceName := strTmp[0]
		backend := strTmp[1]
		bns := strTmp[3]

		var containerInfo cacheutil.ContainerInfo
		err := json.Unmarshal([]byte(instanceInfo), &containerInfo)
		if err != nil {
			ctx.SLog.Warning("unmarshal instance info failed").Set("instance", instanceKey).Set("info", instanceInfo).Print()
			continue
		}

		matchTimeStampCnt := 0
		// 至少 TimeStampCnt(10) 个点下的 clientIP 个数都超过 ClientIPCnt(10) 个
		for timeStamp, forbidMachine := range containerInfo.ForbidMachine {
			// 打日志记录下每个时间戳下的 client IP
			if len(forbidMachine) > int(conf.Application.Migrate.ClientIPCnt) {
				ctx.SLog.Info("instance migrate info for timestamp").Set("instance", instanceName).Set("timeStamp", timeStamp).Set("machine len", len(forbidMachine)).Set("machine", forbidMachine).Print()
				matchTimeStampCnt++
			}
		}

		// 符合迁移条件则加入待迁移列表
		if isInstanceExist(ctx, instanceName, backend) && matchTimeStampCnt >= int(conf.Application.Migrate.TimeStampCnt) {
			bnsInstance, ok := res[bns]
			if !ok {
				bnsInstance = make(map[string]string)
			}

			bnsInstance[instanceName] = instanceKey
			res[bns] = bnsInstance
		}
	}

	cap := humanize.Bytes(hashTableCap)
	// 对单个hashtable 的 len 和 cap 进行日志记录
	ctx.SLog.Info("hashtable info").Set("name", realHashtableName).Set("hashcnt", hashTableLen).Set("cap", cap).Print()

	return res, hashTableLen, hashTableCap, nil
}

// 判断实例是否还存在
func isInstanceExist(ctx *easy.Context, instanceName string, oldBackend string) bool {
	newBackend := common.GetInstanceIPPort(ctx, instanceName)
	if newBackend == oldBackend {
		return true
	}
	return false
}

// 迁移实例过滤
// 过滤占 bns 实例超过 5 % 的实例
func instancesFilter(ctx *easy.Context, whiteListApp []string, instanceData map[string]map[string]string) map[string]map[string]string {
	for bns, instances := range instanceData {
		totalInstanceNum := common.GetBnsInstanceNum(ctx, bns)
		if totalInstanceNum == 0 {
			delete(instanceData, bns)
			continue
		}

		if isWhiteListBns(bns, whiteListApp) {
			ctx.SLog.Info("instance filter whitelist").Set("white list", bns).Print()
			delete(instanceData, bns)
			continue
		}

		migrateInstanceNum := len(instances)
		ratio := 100 * migrateInstanceNum / totalInstanceNum
		// 超过 5 % 则过滤
		if ratio > 5 {
			delete(instanceData, bns)
			// nolint:lll
			ctx.SLog.Warning("migrate instance info").Set("bns name", bns).Set("migrate num", migrateInstanceNum).Set("total num", totalInstanceNum).Set("ratio * 100", ratio).Print()
		} else {
			// nolint:lll
			ctx.SLog.Info("bns migrate info").Set("bns name", bns).Set("migrate num", migrateInstanceNum).Set("total num", totalInstanceNum).Set("ratio * 100", ratio).Print()
		}
	}

	return instanceData
}

// 调用opera接口进行实例迁移
func MigrateInstance(ctx *easy.Context, instanceName string) error {
	// opera中，container 类似 k8s 的 pod， instance类似 k8s 的 container
	// 要进行迁移的是 container ，需要获取真实的 instanceName
	containerName, ok := getContainerName(instanceName)
	if ok {
		ctx.SLog.Info("match get container name").Set("instance", instanceName).Set("container", containerName).Print()
	}

	// 调用 opera 接口获取 taskid
	taskClient := remote.NewTaskClient(ctx)
	header := &remote.TaskHeader{}

	// 测试环境中需要用 test 开头的 user/password/username
	auth := common.GenBasicAuth(loadOperaUser(), loadOperaPassword())
	header.Authorization = auth
	header.ContentType = "application/x-www-form-urlencoded"

	body := &remote.TaskPost{}
	body.Username = loadUserName()

	taskResult, err := taskClient.Do(body, header)
	if err != nil {
		ctx.SLog.Warning("call task failed").SetErr(err).Set("uri", taskClient.GetURI()).Print()
		return err
	}
	ctx.SLog.Notice("task id info").Set("taskid", taskResult.Data.TaskID).Set("uri", taskClient.GetURI()).SetJSON("result", taskResult).Print()
	// 调用opera实例迁移接口进行实例迁移
	taskID := taskResult.Data.TaskID
	mtClient := remote.NewMigrateTaskClient(ctx)

	// 构造动态URI
	url := mtClient.GetURI()
	if len(url) == 0 {
		ctx.SLog.Warning("get task url failed").Set("length", 0).Print()
		return errors.New("get task url failed")
	}
	var realURL string
	if url[len(url)-1] == '/' {
		realURL = fmt.Sprintf("%s%d", url, taskID)
	} else {
		realURL = fmt.Sprintf("%s/%d", url, taskID)
	}
	mtClient.SetURI(realURL)

	// 构造Header
	mtHeader := &remote.MigrateTaskHeader{}
	mtHeader.Authorization = auth
	mtHeader.ContentType = "application/x-www-form-urlencoded"

	// 构造Body
	mtBody := &remote.MigrateTaskPut{}
	// AnyMigrate设置为1，ForceMigrate设置为0，MatrixMigrate设置为0
	mtBody.NeedHealthCheck = 1
	// AnyMigrate设置为1，ForceMigrate设置为0，MatrixMigrate设置为0
	mtBody.NeedHealthPrepare = 0
	// AnyMigrate设置为1，ForceMigrate设置为1，MatrixMigrate设置为0
	mtBody.NeedProcessServiceStatus = 1
	// AnyMigrate设置为1，ForceMigrate设置为1，MatrixMigrate设置为0
	mtBody.NeedResourcePrepare = 0

	mtBody.Username = loadUserName() // 测试环境中需要用测试的代码
	mtBody.InstanceName = containerName
	mtBody.Owner = loadOwnerForOpera()

	mtClient.SetMethod(http.MethodPut)
	_, err = mtClient.Do(mtBody, mtHeader)
	if err != nil {
		var failedData remote.MigrateTaskResultFailedData
		if unmarshalErr := json.Unmarshal(mtClient.Response.Body, &failedData); unmarshalErr != nil {
			ctx.SLog.Warning("unmarshal failed data failed").SetErr(err).Print()
			return unmarshalErr
		}

		ctx.SLog.Warning("migrate instance failed").
			Set("name", containerName).Set("error_message", failedData.ErrorMessage).SetErr(err).Print()
		return err
	}

	ctx.SLog.Info("migrate instance success").Set("name", containerName).Set("task_id", taskID).Print()
	return nil
}

// 缓存每天要迁移的实例，缓存30天
func CacheMigrateInstances(ctx *easy.Context, redis *easy.Redis, instanceBatch []string) error {
	// 天粒度待迁移信息的 service name
	curHashSetForBackend := hashutil.GetCurDayHashTableForBackendName()
	realHashSetCurDay := cacheutil.WithConfigCachePrefix(curHashSetForBackend)

	// 小时粒度的待迁移 service name, 上一个小时的封禁实例存储
	lastHourForbidInstanceHSet := cacheutil.WithConfigCachePrefix((hashutil.GetLastHourHashTableForBackendName()))

	ttl, err := redis.TTL(realHashSetCurDay)
	if err != nil {
		ctx.SLog.Warning("get redis ttl failed").Set("redis-key", realHashSetCurDay).SetErr(err).Print()
		return errors.New("get redis ttl failed")
	}

	instanceLen := len(instanceBatch)
	if instanceLen == 0 {
		ctx.SLog.Warning("migrate instances failed").Set("instances len", len(instanceBatch)).Print()
		return errors.New("instance len is 0")
	}

	// 存储小时粒度的待迁移信息, 用于后续查看
	if err := redis.SAddBatch(lastHourForbidInstanceHSet, instanceBatch); err != nil {
		ctx.SLog.Warning("add instance failed hourly").Set("hour-hashset", lastHourForbidInstanceHSet).Set("len", instanceLen).SetErr(err).Print()
	} else {
		ctx.SLog.Warning("add instance success hourly").Set("hour-hashset", lastHourForbidInstanceHSet).Set("len", instanceLen).SetErr(err).Print()
	}

	// 存储天粒度的待迁移信息
	if err := redis.SAddBatch(realHashSetCurDay, instanceBatch); err != nil {
		ctx.SLog.Warning("add instance failed").Set("day-hashset", realHashSetCurDay).Set("len", instanceLen).SetErr(err).Print()
	} else {
		ctx.SLog.Warning("add instance ").Set("day-hashset", realHashSetCurDay).Set("len", instanceLen).SetErr(err).Print()
	}

	ttlDay := conf.Application.Migrate.MigrationInstanceTTLDay
	if ttlDay == 0 {
		ttlDay = 10
	}
	expireTime := int(ttlDay*60*60*24) + cacheutil.GenRandomTimeout()

	// 小时级别设置 ttl
	if resCnt, err := redis.Expire(lastHourForbidInstanceHSet, expireTime); err != nil {
		ctx.SLog.Warning("set redis key expire time failed").Set("redis-key", lastHourForbidInstanceHSet).Set("return value", resCnt).SetErr(err).Print()
	}

	// 天级别设置 ttl， 设置失败不用抛出错误，直接返回即可, 后续会删除
	if ttl < 0 {
		// 为hashset设置过期时间：30 天 + 随机生成的时间
		resCnt, err := redis.Expire(realHashSetCurDay, expireTime)
		if err != nil {
			ctx.SLog.Warning("set redis key expire time failed").Set("day-hashset", realHashSetCurDay).Set("expiretime", expireTime).Print()
		}
		if resCnt <= 0 {
			ctx.SLog.Warning("set redis key expire time failed").Set("day-hashset", realHashSetCurDay).Set("res", resCnt).Print()
		}
	}

	return nil
}

// 获取白名单app名称。白名单app特征包含：有状态服务
func loadAndUpdateCacheWhiteListApp(ctx *easy.Context, redis *easy.Redis) ([]string, error) {
	appClient := remote.NewOperaAppListClient(ctx)
	query := &remote.OperaAppListGet{}
	query.Username = loadUserName()
	query.ProductlineName = "wangpan"

	header := &remote.OperaAppListHeader{}

	auth := common.GenBasicAuth(loadOperaUser(), loadOperaPassword())
	header.Authorization = auth
	header.ContentType = "application/x-www-form-urlencoded"

	if err := appClient.Do(query, header); err != nil {
		ctx.SLog.Warning("get apps failed").SetErr(err).Print()
		return nil, err
	}
	var dataMap map[string]any
	if err := json.Unmarshal(appClient.Response.Body, &dataMap); err != nil {
		ctx.SLog.Warning("unmarshal apps failed").SetErr(err).Print()
		return nil, err
	}
	appSlice, ok := dataMap["data"].([]any)
	if !ok {
		ctx.SLog.Warning("type asserts failed").Set("type", reflect.TypeOf(dataMap["data"])).Print()
		return nil, errors.New("type asserts failed")
	}

	ctx.SLog.Info("applications info").Set("cnt", len(appSlice)).Print()

	var res []string
	whiteListMap := make(map[string]struct{})
	for _, tmpItem := range appSlice {
		item, ok := tmpItem.(map[string]any)
		if !ok {
			ctx.SLog.Warning("type asserts failed").Set("type", reflect.TypeOf(tmpItem)).Print()
			continue
		}

		appName, ok := isWhiteListApp(item)
		if !ok {
			continue
		}
		res = append(res, appName)
		whiteListMap[appName] = struct{}{}
	}

	if len(res) == 0 {
		// 目前存在设置了迁移回调的app（比如redis等), 命中该分支表明对方接口有问题，或者，判断服务是否为白名单的逻辑有问题
		ctx.SLog.Warning("no whitelist app").Print()
		return nil, errors.New("no whitelist app")
	}

	// 弱依赖，用于后续查看哪些app是白名单，报错不用return
	setName := cacheutil.WithConfigCachePrefix(hashutil.GetWhiteListAppSetName())
	err := redis.SAddBatch(setName, res)
	if err != nil {
		ctx.SLog.Warning("get redis member failed").Set("redis-key", setName).SetErr(err).Print()
	}

	// 将opera请求获取到白名单，和配置里面的白名单合并起来
	whiteListApp := conf.Application.Migrate.WhiteListApp
	for _, app := range whiteListApp {
		if _, ok := whiteListMap[app]; ok {
			continue
		}
		if app == "" {
			continue
		}
		res = append(res, app)
	}

	return res, nil
}

// 如果设置了 call_back_url 的就表明为白名单
func isWhiteListApp(item map[string]any) (string, bool) {
	appName, ok := item["application_name"].(string)
	if !ok {
		return "", false
	}
	url, ok := item["call_back_url"].(string)
	if !ok {
		return "", false
	}

	if url == "" {
		return appName, false
	}

	return appName, true
}

func isWhiteListBns(bns string, whiteListApp []string) bool {
	if _, ok := WhiteListBnsCache[bns]; ok {
		return true
	}

	for _, app := range whiteListApp {
		if strings.Contains(bns, app) {
			WhiteListBnsCache[bns] = struct{}{}
			return true
		}
	}
	return false
}

func getInstanceName(instanceKey string) string {
	slice := strings.Split(instanceKey, "|")
	if len(slice) < 4 {
		return ""
	}
	return slice[0]
}

func getRealInstanceList(ctx *easy.Context, instanceList []string) []string {
	res := make([]string, 0, len(instanceList))
	for _, instance := range instanceList {
		name := getInstanceName(instance)
		if name == "" {
			ctx.SLog.Warning("instance name empty").Set("instancekey", instance).Print()
			continue
		}
		res = append(res, name)
	}
	return res
}

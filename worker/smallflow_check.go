package worker

import (
	"encoding/json"
	"runtime/debug"
	"sync"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

type SvcInfo struct {
	SvcKey        string
	SvcIPsKey     string
	SvcIPValue    string
	SvcMtimeKey   string
	SvcMtimeValue string

	ProblemedType ProblemedType
}

type SvcInfoSlice struct {
	Prefix  string
	SvcInfo []*SvcInfo
}

type ProblemSvcInfoResult struct {
	Key    string
	Prefix []string
}

type ProblemedType string

var ProblemTypeNone ProblemedType = "None"
var ProblemTypeDelay ProblemedType = "Delay"
var ProblemTypeTypeAssertion ProblemedType = "TypeAssertion"

// 定时任务处理的handler
func (s *SmallFlowCheckScheduler) Handler() {
	ctx := easy.NewContext()
	// 入口handler panic捕获，提高代码健壮性，方便问题定位，请谨慎删除
	defer func() {
		if err := recover(); err != nil {
			ctx.SLog.Error("SmallFlowCheckScheduler handler panic").Set("panic", string(debug.Stack())).Print()
			return
		}
	}()
	// 结构化日志打印，详情可参见https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	ctx.SLog.Info("SmallFlowCheckScheduler --------handler").Print()

	prefixs := conf.Application.UfcKeyRightCheckPrefix

	var svcInfoResults []*SvcInfoSlice
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, prefix := range prefixs {
		prefix := prefix

		wg.Add(1)
		go func() {
			defer wg.Done()

			svcInfoResult, err := s.checkSmallflow(ctx, prefix)
			if err != nil {
				ctx.SLog.Warning("checkSmallflow error").Set("prefix", prefix).SetErr(err).Print()
				return
			}
			mu.Lock()
			svcInfoResults = append(svcInfoResults, &SvcInfoSlice{
				Prefix:  prefix,
				SvcInfo: svcInfoResult,
			})
			mu.Unlock()
		}()
	}

	wg.Wait()

	if err := s.handleSvcInfoSlice(ctx, svcInfoResults); err != nil {
		ctx.SLog.Warning("handleSvcInfoSlice error").SetErr(err).Print()
		return
	}
	ctx.SLog.Warning("smallflow check finish").Print()
}

func (s *SmallFlowCheckScheduler) checkSmallflow(ctx *easy.Context, prefix string) ([]*SvcInfo, error) {
	redis := easy.NewRedis(ctx, "redis")

	cntKey := cacheutil.GetUfcServicesCnt(prefix)

	cntValue, err := redis.Get(cntKey)
	if err != nil {
		ctx.SLog.Warning("redis get error").Set("key", cntKey).SetErr(err).Print()
		return nil, err
	}

	cnt, err := conv.ToInt64E(cntValue)
	if err != nil {
		ctx.SLog.Warning("conv.ToInt64E error").Set("value", cntValue).SetErr(err).Print()
		return nil, err
	}

	batch := int64(conf.Application.SmallflowCheck.Batch)
	if batch <= 0 {
		batch = 100
	}

	var start int64 = 0
	var end int64 = 0

	var problemedSvcInfos []*SvcInfo

	for {
		end = start + batch
		if end >= cnt {
			end = cnt
		}

		svcInfos, err := s.checkSmallflowBatch(ctx, prefix, start, end, redis)
		if err != nil {
			ctx.SLog.Warning("checkSmallflowBatch error").Set("prefix", prefix).Set("start", start).Set("end", end).SetErr(err).Print()
			return nil, err
		}
		problemedSvcInfos = append(problemedSvcInfos, svcInfos...)

		start = start + batch
		if start >= cnt {
			break
		}
	}

	return problemedSvcInfos, nil
}

func (s *SmallFlowCheckScheduler) checkSmallflowBatch(ctx *easy.Context, prefix string, start int64, end int64, redis *easy.Redis) ([]*SvcInfo, error) {
	keys := []string{}
	for index := start; index < end; index++ {
		keys = append(keys, cacheutil.GetUfcServiceIndex(prefix, index))
	}
	svcNameSlice, err := redis.MGet(keys)
	if err != nil {
		ctx.SLog.Warning("redis mget error").Set("keys", keys).SetErr(err).Print()
		return nil, err
	}

	svcIPs := make([]string, len(svcNameSlice))
	for i, item := range svcNameSlice {
		svcIPs[i] = cacheutil.GetUfcServiceIPs(prefix, item)
	}

	svcIPValues, err := redis.MGet(svcIPs)
	if err != nil {
		ctx.SLog.Warning("redis mget service ips error").Set("keys", svcIPs).SetErr(err).Print()
		return nil, err
	}

	var svcInfos []*SvcInfo
	var mtimeKeys []string
	for i, item := range svcIPValues {
		if item == "" {
			continue
		}

		svcInfo := &SvcInfo{}

		svcInfo.SvcKey = svcNameSlice[i]
		svcInfo.SvcIPsKey = svcIPs[i]
		svcInfo.SvcIPValue = item
		svcInfo.SvcMtimeKey = cacheutil.GetUfcServiceMtime(prefix, svcInfo.SvcKey)

		svcInfos = append(svcInfos, svcInfo)
		mtimeKeys = append(mtimeKeys, svcInfo.SvcMtimeKey)
	}

	mtimeValues, err := redis.MGet(mtimeKeys)
	if err != nil {
		ctx.SLog.Warning("redis mget service mtime error").Set("keys", mtimeKeys).SetErr(err).Print()
		return nil, err
	}

	if len(mtimeValues) != len(svcInfos) {
		ctx.SLog.Warning("redis mget service mtime error").Set("keys", mtimeKeys).Set("values", mtimeValues).Print()
		return nil, err
	}

	for i, v := range mtimeValues {
		svcInfos[i].SvcMtimeValue = v
	}

	problemedSvcInfos := s.checkProblemedSvcInfos(ctx, svcInfos)

	return problemedSvcInfos, nil
}

func (s *SmallFlowCheckScheduler) checkProblemedSvcInfos(ctx *easy.Context, svcInfos []*SvcInfo) []*SvcInfo {
	var problemed []*SvcInfo
	timeNow := time.Now().Unix()

	ctx.SLog.Info("smallflow check start").Set("timeNow", timeNow).Set("timeout", conf.Application.SmallflowCheck.Timeout).Print()

	for _, svcInfo := range svcInfos {
		svcMtime, err := conv.ToInt64E(svcInfo.SvcMtimeValue)
		if err != nil {
			ctx.SLog.Warning("conv.ToInt64E error").SetErr(err).Print()
			svcInfo.ProblemedType = ProblemTypeTypeAssertion
			problemed = append(problemed, svcInfo)
			continue
		}

		if timeNow-svcMtime > conf.Application.SmallflowCheck.Timeout {
			svcInfo.ProblemedType = ProblemTypeDelay
			problemed = append(problemed, svcInfo)
		}
	}

	return problemed
}

func (s *SmallFlowCheckScheduler) handleSvcInfoSlice(ctx *easy.Context, results []*SvcInfoSlice) error {
	m := make(map[string][]string)
	for _, result := range results {
		prefix := result.Prefix
		for _, item := range result.SvcInfo {
			key := cacheutil.RemoveUfcPrefix(prefix, item.SvcKey)
			m[key] = append(m[key], prefix)
		}
	}

	byt, err := json.Marshal(m)
	if err != nil {
		ctx.SLog.Warning("json marshal error").SetErr(err).Print()
		return err
	}
	retStr := string(byt)

	key := cacheutil.GetIPsLatencyKey()

	redis := easy.NewRedis(ctx, "redis")
	err = redis.SetEX(key, retStr, 172800)
	if err != nil {
		ctx.SLog.Warning("redis set error").SetErr(err).Print()
		return err
	}

	sendHiOption := common.SendHiOption{
		Type:    common.SendHiTypeText,
		Content: retStr,
	}
	ctx.SLog.Info("smallflow check result").Set("result", retStr).Print()

	if err := common.SendHi(conf.Application.HiWarning.AgentLatencyURL, sendHiOption); err != nil {
		ctx.SLog.Warning("send hi error").SetErr(err).Print()
		return err
	} else {
		ctx.SLog.Info("send hi success").Set("result", retStr).Print()
	}

	return nil
}

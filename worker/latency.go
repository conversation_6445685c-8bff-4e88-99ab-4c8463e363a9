package worker

import (
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strconv"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

// 每5min统计一下当前上报延迟的时间超过5min, 然后 Hi 报警

type StatResultInfo struct {
	IP       string
	Latency  int64
	UfcMtime int64
}

// 定时任务处理的handler
func (s *UfcAgentLatencyScheduler) Handler() {
	ctx := easy.NewContext()
	// 入口handler panic捕获，提高代码健壮性，方便问题定位，请谨慎删除
	defer func() {
		if err := recover(); err != nil {
			ctx.SLog.Error("UfcAgentLatencyScheduler handler panic").Set("panic", string(debug.Stack())).Print()
			return
		}
	}()

	ctx.SLog.Info("--------handler").Print()
	timeNow := time.Now()
	timeNowUnix := timeNow.Unix()

	// 处理 ufc-agent 定时更新 < 300 的逻辑
	mtimeZSet := cacheutil.GetUFCIPMtimesZSetName()

	redis := easy.NewRedis(ctx, "redis")

	machineCnt := conf.Application.Latency.CheckMachineCnt
	if machineCnt == 0 {
		machineCnt = 500
	}

	scores, err := redis.ZRangeWithScores(mtimeZSet, 0, machineCnt)
	if err != nil {
		ctx.SLog.Warning("UfcAgentLatencyScheduler get agent mtime by redis  error").Set("mtimeZSet", mtimeZSet).SetErr(err).Print()
		return
	}

	// 处理两类，超过300s(配置更新延迟)
	var tmpStatResultInfoList []*StatResultInfo
	var statResultInfoList []*StatResultInfo

	for i := 0; i+1 < len(scores); i = i + 2 {
		ip := scores[i]
		timestamp, err := strconv.ParseInt(scores[i+1], 10, 64)
		if err != nil {
			ctx.SLog.Warning("UfcAgentLatencyScheduler parse score error").Set("score", timestamp).Print()
			continue
		}

		latencyThreshold := 300
		if conf.Application.Latency.PrivilegeLatencyThreshold != 0 {
			latencyThreshold = conf.Application.Latency.PrivilegeLatencyThreshold
		}

		if timestamp == 0 {
			continue
		}

		latency := timeNowUnix - timestamp

		if latency > int64(latencyThreshold) {
			tmpStatResultInfoList = append(tmpStatResultInfoList, &StatResultInfo{
				IP:       ip,
				Latency:  timeNowUnix - timestamp,
				UfcMtime: timestamp,
			})
		}
	}

	// 过滤ip逻辑
	ips := make([]string, 0)
	for _, item := range tmpStatResultInfoList {
		ips = append(ips, item.IP)
	}

	ips = liveFilter(ctx, ips)  // 是否存活
	ips = inBnsFilter(ctx, ips) // 是否在BNS中

	ipMaps := sliceToMap(ips)
	for _, item := range tmpStatResultInfoList {
		if _, ok := ipMaps[item.IP]; ok {
			statResultInfoList = append(statResultInfoList, item)
		}
	}

	matricID := getMatrixID()
	if len(statResultInfoList) == 0 {
		ctx.SLog.Info("UfcAgentLatencyScheduler no need to alarm").Set("heartbeat enable", conf.Application.Latency.HeartbeatEnable).Print()
		if !conf.Application.Latency.HeartbeatEnable {
			return
		}
		m := map[string]interface{}{
			"machine_cnt_config": machineCnt,
			"latencyThreshold":   300,
			"matrixID":           matricID,
		}
		byt, err := json.Marshal(m)
		if err != nil {
			ctx.SLog.Warning("marshal faield").SetErr(err).Print()
			return
		}

		msg := "UfcAgentLatencyScheduler heartbeat: no need to send\n\n" + string(byt)
		option := common.SendHiOption{
			Type:    common.SendHiTypeText,
			Content: msg,
		}

		if err := common.SendHi(conf.Application.HiWarning.AgentLatencyURL, option); err != nil {
			ctx.SLog.Warning("send hi error").Set("msg", msg).SetErr(err).Print()
		}

		return
	}

	byt, err := json.Marshal(statResultInfoList)
	if err != nil {
		ctx.SLog.Warning("marshal agent latency faield").SetErr(err).Print()
		return
	}

	byteStr := string(byt)

	key := cacheutil.GetAgentLatencyKey(timeNow.Format("200601021504"))
	if err := redis.SetEX(key, byteStr, 86400); err != nil {
		ctx.SLog.Warning("set agent latency key error").Set("key", key).SetErr(err).Print()
		return
	}

	msg := "UfcAdminEasy: UfcAgent配置更新时间间隔过长\n"
	msg += fmt.Sprintf("配置更新条目: %d\n", len(statResultInfoList))
	msg += fmt.Sprintf("全部延迟数据见redis key: %s （如流报警有字数限制，如果下方有截断表示超出字数限制）\n", key)
	msg += "\n" + string(byteStr)

	if len(msg) > 1800 {
		msg = msg[:1800]
	}

	option := common.SendHiOption{
		Type:    common.SendHiTypeText,
		Content: msg,
	}

	if err := common.SendHi(conf.Application.HiWarning.AgentLatencyURL, option); err != nil {
		ctx.SLog.Warning("send hi error").SetErr(err).Print()
		return
	}

}

# name, app 在云控注册的服务名
name: UfcAdminEasy

# open 设置是否开启 heartbeat 能力
open: true

# force_start 设置启动时与云端同步失败后能否继续完成进程启动
force_start: true

# method 设置请求云端时使用的 http method
method: POST

# interactive_service_name 设置请求云端使用的 interactive name
interactive_service_name: tangram_cloud_sync_backend

# url 设置请求云端的 path
url: /rest/2.0/pvcp/tangram?method=heartbeat

# interval 设置与请求云端的时间间隔，当初次请求成功后，时间间隔采用由云端下发的配置
interval: 30s
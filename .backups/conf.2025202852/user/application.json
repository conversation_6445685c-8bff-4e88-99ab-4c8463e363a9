{"migrate": {"intervalTime": 5, "hashTableTTL": 24, "clientIPMaxCntForInterval": 20, "clientIPCnt": 10, "timeStampCnt": 10, "redisHashTableCntForService": 100, "migrationInstanceTTLDay": 10, "migrationMaxCnt": 300, "migrationEabled": false, "whiteListApp": ["unamecache", "pcsFileConsistency"]}, "hiWarning": {"url": "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dfc0a6901ba9b51517f42ad495d60fcdc", "opUrl": "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dfc0a6901ba9b51517f42ad495d60fcdc", "agentLatencyUrl": "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dfc0a6901ba9b51517f42ad495d60fcdc", "fullDiskUrl": "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dfc0a6901ba9b51517f42ad495d60fcdc", "ufcLogRotationUrl": "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dfc0a6901ba9b51517f42ad495d60fcdc", "otherUrl": "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dfc0a6901ba9b51517f42ad495d60fcdc", "appEnable": true, "noAppEable": false}, "ufcDeploy": {"enable": true, "deployThreshold": 30}, "watchdog": {"hiEnable": true, "DiskFuncHiEnable": true, "UfcLogHiEnable": true, "handleInterval": {"UfcLogInterval": 86400, "DiskFullInterval": 3600, "OtherInterval": 86400}, "hiFilter": {"msgFiltList": ["no log files found"], "titleFiltList": ["failed to get log files"], "diskThreshold": 99, "ufcDiskThreshold": 21474836480}, "hiAtOpEnable": false}, "latency": {"hiEnable": true, "privilegeLatencyThreshold": 300, "checkMachineCnt": 500, "heartbeatEnable": true}, "isOnline": true, "configUpdateStatEnable": true, "enableInPerRequest": false, "enableInTimer": true, "deleteZset": true, "ufcBns": ["group.ufc-split-all.cloud-storage.all"], "ufcKeyRightCheckPrefix": ["p-3ufc-beijing", "p-3<PERSON><PERSON>-ya<PERSON><PERSON>n", "p-3ufc-xian"], "uploadVersionEnable": false, "smallflowCheck": {"timeout": 172800, "batch": 100}}
# 设置进程监听端口的 Server 的相关配置

# mars_server 是用户使用时为 server 起的一个唯一性名字，每个 server 的名字应当都不同
mars_server:

  # server_type 设置该 server 配置启动的 server 类型，目前支持 mars_http ｜ rest_http 两种，都是 http/https 类型的 server
  server_type: mars_http

  # middleware 设置 server 使用的服务治理类型，网盘服务应该默认使用 govern，其他服务可不写，目前 govern 主要支持 http 请求的语义级治理能力、以及全类型的重试策略
  middleware: builtin

  # network 设置 server 启动的 net 类型
  network: tcp

  # addr 设置 server 启动的地址，可以是端口号 "8057"、":8057"、"0.0.0.0:8057"，或者在 opera 部署时使用端口名 "main","port_N" 等
  # 对使用端口名的服务在启动时会由框架完成端口号的查找和替换，不需要用户再手动在启动前执行端口替换逻辑
  addr: 8057

  # cert_file 设置 https server 使用的证书文件地址
  cert_file: ""

  # key_file 设置 https server 使用的 key 文件地址
  key_file: ""

  # access_file 设置 server 请求日志输出日志配置，值应当存在于 logger 的配置
  access_file: access

  # access_format 设置 access 日志输出时的格式，支持自定义输出格式和内容，可以在代码中通过 Access.Set 自定义增加 key 和 value
  # tangram 会默认将 ${var} 或 $var 替换为对应的值，format 可以配置为 log_id=$log_id 形式，也可以直接配置为 $log_id 形式
  # 以下为 tangram 默认可选字段
  # $log_id $call_id $host $client_ip $client_port $method $handle_path $uri $path $query_string $user_agent $request_content_length $response_body_length $status $cost_time
  access_format: log_id=$log_id call_id=$call_id host=$host client_ip=$client_ip client_port=$client_port method=$method handle_path=$handle_path path=$path uri=$uri user_agent=$user_agent request_content_length=$request_content_length response_body_length=$response_body_length status=$status cost_time=$cost_time errno=$errno
  
  # gracefully_shutdown_time 设置进程收到退出信号(kill)时，server 最长延迟退出时间，用于保证 server 可以在处理完已建立连接的请求后再退出
  # 设置的时间需要带有时间单位："ns", "us" (or "µs"), "ms", "s", "m", "h"
  # 默认最长时间 1m
  gracefully_shutdown_time: 1m

/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: agent模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/ufc-admin-easy/dto/rest/2.0/agent"
	service "icode.baidu.com/baidu/netdisk/ufc-admin-easy/service/rest/2.0/agent"
)

// Agent 路由接口对应的Controller,包括入参Dto以及service
type AgentController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	AgentService *service.AgentService
	UploadDto    *dto.UploadReqDto
}

// ufc-agent 错误信息上报
func (t *AgentController) Upload() {
	easy.Exec(t)
}

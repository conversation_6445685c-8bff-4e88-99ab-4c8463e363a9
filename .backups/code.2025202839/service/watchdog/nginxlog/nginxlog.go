/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: nginxlog业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"path/filepath"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	dto "icode.baidu.com/baidu/netdisk/ufc-admin-easy/dto/watchdog/nginxlog"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/entity/remote"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/fileutil"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type NginxlogService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
}

// 初始化 必须传入上下文
func NewNginxlogService(ctx *easy.Context) *NginxlogService {
	service := &NginxlogService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewNginxlogService 后都会走的逻辑
 */
func (t *NginxlogService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * show - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *NginxlogService) Show(req *dto.ShowReqDto) ([]byte, error) {
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	backend := req.Backend
	if backend == "" {
		t.SLog.Warning("show by local").Set("filename", req.Filename).Set("backend", backend).Print()
		return t.showByLocal(req.Filename)
	}
	t.SLog.Warning("show by remote").Set("filename", req.Filename).Set("backend", backend).Print()
	return t.showByRemote(backend, req.Filename)
}

func (t *NginxlogService) showByLocal(fileName string) ([]byte, error) {
	file := filepath.Join(fileutil.FileDir, fileName)
	fileBytes, err := fileutil.GetFileContent(file)
	if err != nil {
		t.SLog.Error("get file content error").Set("file", file).SetErr(err).Print()
		return nil, err
	}
	return fileBytes, nil

}

func (t *NginxlogService) showByRemote(backend string, fileName string) ([]byte, error) {
	logShowClient := remote.NewNginxLogShowClient(t.Context)
	t.SLog.Warning("show by remote").Set("backend", backend).Set("filename", fileName).Print()
	logShowClient.SetToServiceUfcName(backend) // backend 也可以为 ip:port 格式，api 有歧义

	query := &remote.NginxLogShowGet{
		Filename: fileName,
		Backend:  "",
	}

	err := logShowClient.Do(query)
	if err != nil {
		t.SLog.Error("show by remote error").Set("backend", backend).Set("filename", fileName).SetErr(err).Print()
		return nil, err
	}

	return logShowClient.Response.Body, nil
}

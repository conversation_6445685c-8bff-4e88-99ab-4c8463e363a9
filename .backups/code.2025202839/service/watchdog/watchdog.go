/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: watchdog业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/assert"
	dto "icode.baidu.com/baidu/netdisk/ufc-admin-easy/dto/watchdog"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/watchdogutil"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type WatchdogService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
}

// 初始化 必须传入上下文
func NewWatchdogService(ctx *easy.Context) *WatchdogService {
	service := &WatchdogService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewWatchdogService 后都会走的逻辑
 */
func (t *WatchdogService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * alert - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *WatchdogService) Alert(req *dto.AlertReqDto) (*dto.AlertResDto, error) {
	res := &dto.AlertResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	typ := req.BodyDto.Type
	if typ != watchdogutil.AlertTypeDisk && typ != watchdogutil.AlertTypeNginxLog {
		typ = "other"
	}

	watchDogHsetName := cacheutil.GetWatchDogHSetName(typ)

	ipKey := t.Context.Context.Request.ClientIP()

	timeNow := time.Now().Unix()

	if !conf.Application.Watchdog.HiEnable {
		return res, nil
	}
	timestamp, err := t.Redis.HGet(watchDogHsetName, ipKey)

	isRedisErrNil := assert.IsRedisErrNil(err)
	if err != nil && !isRedisErrNil {
		t.SLog.Error("alert redis hget error").Set("key", watchDogHsetName).Set("filed", ipKey).SetErr(err).Print()
		return res, nil
	}

	if !isRedisErrNil {
		interval := conf.Application.Watchdog.HandleInterval.OtherInterval
		switch typ {
		case watchdogutil.AlertTypeDisk:
			interval = conf.Application.Watchdog.HandleInterval.DiskFullInterval
		case watchdogutil.AlertTypeNginxLog:
			interval = conf.Application.Watchdog.HandleInterval.UfcLogInterval
		}

		timestampInt, err := strconv.Atoi(timestamp)
		if err == nil && timeNow-int64(timestampInt) < interval {
			t.SLog.Error("alert too fast").Set("key", watchDogHsetName).Set("ip", ipKey).Set("type", typ).Set("remote_ts", timestampInt).Set("interval", interval).Set("time", timeNow).Print()
			return res, nil
		}

	}

	var alertInfo watchdogutil.WatchDogAlert
	rawBody := t.Context.Context.Request.RawBody()

	if err := json.Unmarshal(rawBody, &alertInfo); err != nil {
		t.SLog.Error("alert unmarshal error").SetErr(err).Print()
		return res, nil
	}

	if watchdogutil.IsFilterMsg(&alertInfo) {
		return res, nil
	}

	// 是否开启alertInfo
	if !watchdogutil.IsHiEnable(typ) {
		return res, nil
	}

	opURL := conf.Application.HiWarning.OpURL

	// ufc 日志未切分先汇聚
	if typ == watchdogutil.AlertTypeNginxLog {
		hset := cacheutil.GetUfcLogRotationHSet(cacheutil.GetCurHour())
		if err := t.Redis.HSet(hset, ipKey, string(rawBody)); err != nil {
			t.SLog.Error("hset ufc log rotation error").Set("key", hset).Set("filed", ipKey).SetErr(err).Print()
		}

		return res, nil
	}

	// 后续 OP 如果有不想接受到某一类型的告警，可以单独处理
	if typ == watchdogutil.AlertTypeDisk {
		opURL = conf.Application.HiWarning.FullDiskURL
	}
	if typ == watchdogutil.AlertTypeOther {
		opURL = conf.Application.HiWarning.OtherURL
	}

	msg := t.formatHiMsg(typ, &alertInfo)

	// 发送错误的问题不用返回给上层 watchdog，这里打日志即可
	sendHiOption := common.SendHiOption{
		Type:    common.SendHiTypeMD,
		Content: msg,
	}

	// 发给 UfcAdminEasy 报警群
	if err := common.SendHi(conf.Application.HiWarning.URL, sendHiOption); err != nil {
		t.SLog.Error("alert send hi error").SetErr(err).Print()
	} else {
		t.SLog.Error("alert send hi success").Set("msg", msg).Print()
	}

	// 发给OP群
	if conf.Application.Watchdog.HiAtOpEnable && !common.IsTodayWeekend() {
		opList, err := watchdogutil.GetZhibanOPList(t.Context)
		if err != nil {
			opList = []string{}
		}
		sendHiOption.AtUserIDs = opList
		sendHiOption.NeedAt = true
		if err := common.SendHi(opURL, sendHiOption); err != nil {
			t.SLog.Error("alert send op hi error").SetErr(err).Print()
			return nil, err
		}
	}

	// 存储告警信息
	if err := t.Redis.HSet(watchDogHsetName, ipKey, fmt.Sprint(timeNow)); err != nil {
		t.SLog.Error("alert redis hset error").Set("key", watchDogHsetName).Set("filed", ipKey).SetErr(err).Print()
	}

	return res, nil
}

func (t *WatchdogService) formatHiMsg(typ string, alertInfo *watchdogutil.WatchDogAlert) string {
	var title string
	switch typ {
	case watchdogutil.AlertTypeDisk:
		title = "Ufc Watchdog: 磁盘满告警"
	case watchdogutil.AlertTypeNginxLog:
		title = "Ufc Watchdog: nginx日志超过阈值告警(日志未切分)"
	default:
		title = "Ufc Watchdog: 未知报警"
	}

	matrixName := os.Getenv("MATRIX_INSTANCE_ID")
	if matrixName == "" {
		matrixName, _ = os.Hostname()
	}

	// 过滤不需要的详情字段
	filteredDetails := make(map[string]interface{})
	for k, v := range alertInfo.Details {
		if k != "check_patterns" && k != "check_type" &&
			k != "alert_threshold" && k != "files" {
			filteredDetails[k] = v
		}
	}

	detailsJSON, _ := json.Marshal(filteredDetails)

	msg := fmt.Sprintf(`## %s


**Host**: %s  

**Message**: %s  

**From Instance**: %s (OP无需关注该字段)  

### 告警详情
`, title, alertInfo.Hostname, alertInfo.Message, matrixName)

	// 添加过滤后的详情内容
	msg += "```json\n" + string(detailsJSON) + "\n```"

	// 确保JSON完整性后再截断
	if len(msg) > 1800 {
		// 保留足够的空间给JSON闭合
		msg = msg[:1790] + "\n...\n```"
	}

	return msg
}

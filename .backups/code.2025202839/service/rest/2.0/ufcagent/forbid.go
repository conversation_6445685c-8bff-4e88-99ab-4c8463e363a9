// 处理 upload 接口的封禁实例信息
package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"sync"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/hashutil"
)

type containerInfo struct {
	LastForbidTime int64                            `json:"last_forbidtime"`
	ForbidMachine  map[string](map[string]struct{}) `json:"forbid_machine"`
}

func (t *UfcagentService) DoForbid(forbidInfo any) error {
	if forbidInfo == nil {
		return nil
	}
	clientIP := t.Context.Request.ClientIP()

	forbidList, ok := forbidInfo.(map[string]any)
	if !ok {
		return fmt.Errorf("forbid info type is not map[string]interface{}, is %s", reflect.TypeOf(forbidInfo))
	}

	backendList, ok := forbidList["backend"].([]any)
	if !ok {
		return nil
	}

	var wg sync.WaitGroup
	wg.Add(len(backendList))
	for _, bk := range backendList {
		bk := bk
		go func() { // 封禁实例信息处理, 逻辑较重，用协程去做
			defer wg.Done()
			item, ok := bk.(map[string]any)
			if !ok {
				return
			}

			backend := common.GetStr(item["backend"])
			ufcService := common.GetStr(item["service"])
			newCtx, err := easy.WithOldContext(t.Context)
			if err != nil {
				t.SLog.Warning("context create failed").SetErr(err).Print()
				return
			}
			t.migrateInfoCollect(newCtx, ufcService, backend, clientIP)
		}()
	}
	wg.Wait()

	return nil
}

// 封禁信息收集
func (t *UfcagentService) migrateInfoCollect(ctx *easy.Context, serviceName string, backend string, clientIP string) {
	redis := easy.NewRedis(ctx, "redis")

	containerID, bns, err := t.getContainerIDByService(redis, serviceName, backend)
	if err != nil {
		t.SLog.Warning("get container id failed").Set("service", serviceName).Set("backend", backend).SetErr(err).Print()
		return
	}
	// 获取要存放的hashtable
	curHashTableName, err := hashutil.CurHourHashTableKey(serviceName)
	if err != nil {
		t.SLog.Warning("get hash table key failed").Set("service", serviceName).Set("backend", backend).SetErr(err).Print()
		return
	}
	realHashTableName := cacheutil.WithConfigCachePrefix(curHashTableName)

	// 把hashtable名称存到hashset 中，供后续遍历
	hashSet, err := hashutil.GetCurHashSet()
	if err != nil {
		t.SLog.Warning("get hash set failed").Set("service", serviceName).Set("backend", backend).SetErr(err).Print()
		return
	}
	realHashSet := cacheutil.WithConfigCachePrefix(hashSet)

	hashSetTTL, err := redis.TTL(realHashSet)
	if err != nil {
		t.SLog.Warning("get hashset ttl failed").Set("service", serviceName).Set("backend", backend).SetErr(err).Print()
		return
	}

	ttl, err := redis.TTL(realHashTableName)
	if err != nil {
		t.SLog.Warning("get hashtable ttl failed").Set("service", serviceName).Set("hashtable", realHashTableName).SetErr(err).Print()
		return
	}

	hashField := fmt.Sprintf("%s|%s|%s|%s", containerID, backend, serviceName, bns)

	var containerInfo containerInfo
	containerInfo.ForbidMachine = make(map[string]map[string]struct{})

	hashTableExist, err := redis.Exist(realHashTableName)
	if err != nil {
		t.SLog.Warning("get hash table exist failed").Set("hashtable", realHashTableName).Set("hashfield", hashField).SetErr(err).Print()
		return
	}
	timeNow := time.Now().Unix()
	if hashTableExist {
		res, err := redis.HGet(realHashTableName, hashField)
		if err == nil && res != "" {
			if err := json.Unmarshal([]byte(res), &containerInfo); err != nil {
				t.SLog.Warning("unmarshal containerInfo failed").Set("info", res).SetErr(err).Print()
				return
			}

			lastForbidTime := containerInfo.LastForbidTime
			// 比较上一次更新时间, intervalue默认为 5 min
			// 如果超过 2 * intervaltime，则将整个 containerInfo 的记录清除，并重新统计
			// 如果超过 1 * intervaltime, 则另起一个时间戳，把对应记录记到该时间戳下，且更新lastForBidTime
			// 如果小于 1 * intervaltime, 则将本次封禁记录记到上一次封禁时间戳下，且不更新封禁时间lastForBidTime

			// 超过 2 * intervaltime, 清空，供后续重新统计
			if lastForbidTime != 0 && timeNow-lastForbidTime > int64(2*60*conf.Application.Migrate.IntervalTime) {
				containerInfo.ForbidMachine = make(map[string]map[string]struct{})
				containerInfo.LastForbidTime = 0
			}
		}
	}

	lastForbidTime := containerInfo.LastForbidTime
	// 超过 1 * intervaltime, 另起一个时间戳，记录该clientip到该时间戳下
	if timeNow-lastForbidTime > int64(60*conf.Application.Migrate.IntervalTime) {
		tmpMap := make(map[string]struct{})
		tmpMap[clientIP] = struct{}{}
		containerInfo.ForbidMachine[strconv.FormatInt(timeNow, 10)] = tmpMap
		containerInfo.LastForbidTime = timeNow
	} else {
		// 小于 1 * intervaltime
		tmpMap, ok := containerInfo.ForbidMachine[strconv.FormatInt(lastForbidTime, 10)]
		if !ok {
			tmpMap = make(map[string]struct{})
		}

		// 1个时间戳下记录的ip个数不超过以下配置中的个数，这里配置暂为20个
		if len(tmpMap) < int(conf.Application.Migrate.ClientIPMaxCntForInterval) {
			tmpMap[clientIP] = struct{}{}
			containerInfo.ForbidMachine[strconv.FormatInt(lastForbidTime, 10)] = tmpMap
		}
	}

	containerInfoStr, err := json.Marshal(containerInfo)
	if err != nil {
		t.SLog.Warning("marshal containerInfo failed").Set("info", containerInfoStr).SetErr(err).Print()
		return
	}

	// 把封禁信息记录到 hashtable 中
	if err := redis.HSet(realHashTableName, hashField, string(containerInfoStr)); err != nil {
		t.SLog.Warning("set hashtable value of field failed").Set("hashtable", realHashTableName).Set("hashfield", hashField).Set("value", containerInfoStr).Print()
		return
	}
	// 记录成功也打个日志用于排查
	t.SLog.Info("set hashtable filed success").Set("hashtable", realHashTableName).Set("hashfield", hashField).Print()

	// 把表名存到 hashset 中，供后续 worker 遍历
	if err := redis.SAdd(realHashSet, realHashTableName); err != nil {
		t.SLog.Warning("add hash set failed").Set("service", serviceName).Set("hashtable", realHashTableName).Set("hashset", realHashSet).SetErr(err).Print()
		return
	}

	// 都不超时，不需设置超时时间
	if ttl >= 0 && hashSetTTL >= 0 {
		return
	}

	ttlHour := conf.Application.Migrate.HashTableTTL
	if ttlHour <= cacheutil.TTLHourMin || ttlHour > cacheutil.TTLHourMax {
		ttlHour = cacheutil.TTLHourDefault
	}

	// hashtable 一开始不存在则给它设置一个过期时间
	if ttl < 0 {
		// 设置过期时间：固定 TTL 小时(暂定24小时) + 随机生成的时间
		expireTime := int(ttlHour*3600) + cacheutil.GenRandomTimeout()
		resCnt, err := redis.Expire(realHashTableName, expireTime)
		if err != nil {
			t.SLog.Warning("set hash table expire failed").Set("hashtable", realHashTableName).Set("expiretime", expireTime).Print()
		}
		if resCnt <= 0 {
			t.SLog.Warning("set hash table expire failed").Set("hashtable", realHashTableName).Set("res", resCnt).Print()
		}
	}

	if hashSetTTL < 0 {
		// 为hashset设置过期时间：固定 TTL 小时(暂定24小时) + 随机生成的时间
		expireTime := int(ttlHour*3600) + cacheutil.GenRandomTimeout()
		resCnt, err := redis.Expire(realHashSet, expireTime)
		if err != nil {
			t.SLog.Warning("set hash set expire failed").Set("hashset", realHashSet).Set("expiretime", expireTime).Print()
		}
		if resCnt <= 0 {
			t.SLog.Warning("set hash set expire failed").Set("hashset", realHashSet).Set("res", resCnt).Print()
		}
	}
}

// 根据service name, ip 获取 containerID, bns
func (t *UfcagentService) getContainerIDByService(redis *easy.Redis, serviceName, backend string) (string, string, error) {
	realSvcKey := cacheutil.WithP3UFCPrefix("service-" + serviceName)
	svcConfigStr, err := redis.Get(realSvcKey)
	if err != nil {
		t.SLog.Warning("get service config failed").Set("realkey", realSvcKey).SetErr(err).Print()
		return "", "", err
	}

	svcConfig := make(map[string]any)
	err = json.Unmarshal([]byte(svcConfigStr), &svcConfig)
	if err != nil {
		t.SLog.Warning("service config not a json").SetErr(err).Print()
		return "", "", err
	}

	bnsList, err := getBnsFromSvcConfig(svcConfig)
	if err != nil {
		return "", "", err
	}

	for _, bns := range bnsList {
		containerID := common.GetContainerIDByBns(t.Context, bns, backend)
		if containerID != "" {
			return containerID, bns, nil
		}
	}

	return "", "", errors.New("can not find backend in bns")
}

// 从 service config 中读取 bns 列表
func getBnsFromSvcConfig(svcConfig map[string]any) ([]string, error) {
	bnsMap, ok := svcConfig["bns_list"].(map[string]any)
	if !ok {
		return nil, errors.New("service config not a json")
	}

	var bnsList []string
	for bns := range bnsMap {
		bnsList = append(bnsList, bns)
	}

	return bnsList, nil
}

/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: UfcagentService单元测试
 */
package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
	dto "icode.baidu.com/baidu/netdisk/ufc-admin-easy/dto/rest/2.0/ufcagent"
)

// handle接口业务测试入口
func TestUfcagentHandle(t *testing.T) {
	// 注册驱动
	UfcagentMockRegistry(t)
	s := NewUfcagentService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.HandleReqDto{}
	// 设置reqDto参数 测试数据

	resDto, errDto := s.Handle(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// 注册驱动
func UfcagentMockRegistry(t *testing.T) {
	var err error
	// 初始化配置文件,如果在根目录启动则不用设置
	config_mock.SetConfRootPath("../conf")
	err = easy.Init("")
	assert.Nil(t, err)
	// 注册直连sql驱动
	// 注册直连redis驱动
	err = mock.MockRedisDirectRegistry("redis", "************:8841")
	assert.Nil(t, err)
	// 注册直连http驱动, 测试的host链接由业务指定
	//示例:err = mock.MockHttpDirectRegistry("confName","http://localhost:8080")
	err = mock.MockHttpDirectRegistry("nacmaster", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("naclocal", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("MigrateTask", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("Task", "")
	assert.Nil(t, err)
}

package service

import (
	"errors"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
)

func saveVersion(ctx *easy.Context, uploadVersion string) error {
	redis := easy.NewRedis(ctx, "redis")

	clientIP := ctx.Request.ClientIP()
	if clientIP == "" {
		ctx.SLog.Warning("get client ip failed").Print()
		return errors.New("get client ip failed")
	}

	versionHSet := cacheutil.GetVersionHSetName()
	if err := redis.HSet(versionHSet, clientIP, uploadVersion); err != nil {
		ctx.SLog.Warning("ufc version upload failed").Set("client ip", clientIP).Set("upload version", uploadVersion).SetErr(err).Print()
		return err
	}

	return nil
}

/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: agent业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/ufc-admin-easy/dto/rest/2.0/agent"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/localcache"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type AgentService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
}

// 初始化 必须传入上下文
func NewAgentService(ctx *easy.Context) *AgentService {
	service := &AgentService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}

	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewAgentService 后都会走的逻辑
 */
func (t *AgentService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * upload - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *AgentService) Upload(req *dto.UploadReqDto) (*dto.UploadResDto, error) {
	res := &dto.UploadResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	// errorType 为 1 时表明hash 负载均衡时但没有传 hash key
	bodySlice := req.BodyDto
	var keys []string
	for _, item := range bodySlice {
		keys = append(keys, item.Key)
		t.SLog.Warning("ufc agent upload error msg").
			Set("key", item.Key).Print()
	}
	localcache.IncreBatch(keys)

	return res, nil
}

package netutil

import (
	"context"
	"net"
	"os/exec"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// 指定内部 dns server
var defaultResolver = &net.Resolver{
	PreferGo: true, // 强制使用 Go 内部解析器
	Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
		// 这里指定 DNS 服务器，例如 Google 的 *******:53
		d := net.Dialer{Timeout: time.Second * 5} // 设置超时时间
		return d.DialContext(ctx, "udp", "***********:53")
	},
}

func GetHostnameByIP(ctx *easy.Context, clientIP string) string {
	var hostname []string
	var err error

	for i := 0; i < 3; i++ {
		hostname, err = defaultResolver.LookupAddr(context.Background(), clientIP)
		if err != nil {
			ctx.SLog.Warning("get hostname by ip error").Set("try times", i).Set("ip", clientIP).Set("hostname", hostname).SetErr(err).Print()
			continue
		}
		break
	}
	if err != nil {
		ctx.SLog.Error("get hostname by ip failed").SetErr(err).Print()
		return ""
	}

	if len(hostname) == 0 {
		return ""
	}

	return hostname[0]
}

func GetIPByHostname(ctx *easy.Context, hostname string) string {
	var ips []string
	var err error

	for i := 0; i < 3; i++ {
		ips, err = defaultResolver.LookupHost(context.Background(), hostname)
		if err != nil {
			ctx.SLog.Warning("get ip by hostname error").Set("try times", i).Set("hostname", hostname).Set("ip", ips).SetErr(err).Print()
			continue
		}
		break
	}
	if err != nil {
		ctx.SLog.Error("get ip by hostname failed").SetErr(err).Print()
		return ""
	}

	if len(ips) == 0 {
		return ""
	}

	return ips[0]
}

func IsOnlineInstance(ctx *easy.Context, ip string) bool {
	var execRes []byte
	var err error
	for i := 0; i < 3; i++ {
		execRes, err = exec.Command("get_service_by_host", "-i", ip).Output()
		if err != nil {
			ctx.SLog.Error("exe command get_service_by_host error").Set("try times", i).Set("ip", ip).SetErr(err).Print()
			continue
		}
		break
	}

	if err != nil {
		ctx.SLog.Error("exe command get_service_by_host failed").SetErr(err).Print()
		return false
	}

	execResStr := string(execRes)
	if strings.Contains(execResStr, "orp") || strings.Contains(execResStr, "opera") {
		return true
	}
	return false
}

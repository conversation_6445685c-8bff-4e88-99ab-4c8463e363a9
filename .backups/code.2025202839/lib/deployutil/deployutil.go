package deployutil

import (
	"os/exec"
	"regexp"
	"strings"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/entity/remote"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

const (
	// 使用前需要配置特殊验证，http://giano.baidu.com/soc/baas#/authenticate/home/<USER>
	// 具体可以咨询OP
	BaasUser  = "zhongzhenyan"
	BaasRole  = "baas_all_privilege"
	BaasGroup = "baas_default_group"
)

func UfcDeploy(ctx *easy.Context, ipList []string) error {
	if !conf.Application.UfcDeploy.Enable {
		ctx.SLog.Info("UfcDeploy is disabled").Print()
		return nil
	}
	ctx.SLog.Info("UfcDeploy is enabled").Print()

	client := remote.NewUfcDeployClient(ctx)

	hostStr := strings.Join(ipList, "\n")

	body := &remote.UfcDeployPost{
		Username:    "auto-repairer",
		Target:      "pcs_ufc_longtail_deal",
		RepaireType: "reboot",
		Reason:      "UfcAdminEasy Auto Repair",
		Hosts:       hostStr,
	}

	cred, err := getBaaSCred(ctx, BaasUser, BaasRole, BaasGroup)
	if err != nil {
		ctx.SLog.Warning("getBaaSCred error").SetErr(err).Print()
		return err
	}

	header := &remote.UfcDeployHeader{
		Cred: cred,
	}

	if err := client.Do(body, header); err != nil {
		ctx.SLog.Error("UfcDeployClient Do error").SetErr(err).Print()
		return err
	}

	ctx.SLog.Info("UfcDeployClient Do success").Set("resp", string(client.Response.Body)).Print()
	return nil
}

// GetBaaSCred 获取BAAS凭证
// baasUser: BAAS用户名
// baasRole: BAAS角色(默认为baas_all_privilege)
// baasGroup: BAAS组(默认为baas_default_group)

// 这个函数线下无法测试，需要在线上环境跑
func getBaaSCred(ctx *easy.Context, baasUser string, baasRole string, baasGroup string) (string, error) {
	// 设置默认值
	if baasRole == "" {
		baasRole = "baas_all_privilege"
	}
	if baasGroup == "" {
		baasGroup = "baas_default_group"
	}

	// 1. 执行 baas login 命令
	loginCmd := exec.Command("baas", "login",
		"--baas_user="+baasUser,
		"--baas_role="+baasRole,
		"--baas_group="+baasGroup)

	if err := loginCmd.Run(); err != nil {
		ctx.SLog.Warning("baas login error").Set("user", baasUser).SetErr(err).Print()
		// log.Printf("machine reboot job init error: baas bind error for user %s", baasUser)
		return "", err
	}

	// 2. 检查 baas query 结果
	queryCmd := exec.Command("baas", "query", "--session_default")
	output, err := queryCmd.CombinedOutput()
	if err != nil {
		// log.Printf("machine reboot job init error: baas info query error for user %s", baasUser)
		ctx.SLog.Warning("bass query error").Set("user", baasUser).Set("output", string(output)).SetErr(err).Print()
		return "", err
	}

	if strings.Contains(string(output), "BAAS_QUERY_AGENT_INFO_FAILURE") {
		return "", err
	}

	// 3. 获取用户名并生成凭证
	userCmd := exec.Command("bash", "-c", "baas query --session_default | grep PUser | awk '{print $2}'")
	userOutput, err := userCmd.Output()
	if err != nil {
		return "", err
	}
	user := strings.TrimSpace(string(userOutput))

	genCredCmd := exec.Command("baas", "gen_cred", "--baas_user="+user)
	credOutput, err := genCredCmd.Output()
	if err != nil {
		ctx.SLog.Warning("baas gen_cred error").Set("user", user).Set("output", credOutput).SetErr(err).Print()
		return "", err
	}

	// 4. 使用正则表达式提取凭证
	re := regexp.MustCompile(`(?s)Credential":"(\S+?)"`)
	matches := re.FindStringSubmatch(string(credOutput))
	if len(matches) < 2 {
		ctx.SLog.Warning("baas gen_cred output does not contain credential").Set("output", credOutput).Print()
		return "", nil
	}

	return matches[1], nil
}

/*
* Easy生成，**平台修改本地update会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: 常量以及数据库信息引用
* Doc: https://ku.baidu-int.com/d/QFI7Jr_SnCBKKI
 */
package consts

import "icode.baidu.com/baidu/netdisk/easy-go-sdk"

// DB信息定义
type Ufc struct {
	easy.DBStruct
	Tables struct {
		UfcAgentElapseInfo easy.TableName
	}
}

// 初始化数据库和表名的映射
var Dbs struct {
	Ufc Ufc
}

// 在 init 函数中为 Dbs 进行初始化赋值
func init() {
	Dbs.Ufc.Name = "ufc"
	Dbs.Ufc.ConfName = "mysql_cloud_ufc"
	Dbs.Ufc.Tables.UfcAgentElapseInfo = "ufc_agent_elapse_info"
}

package common

import (
	"fmt"
	"strconv"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/bns"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/protobuf/proto"
)

func GetContainerIDByBns(ctx *easy.Context, bnsName string, backend string) string {
	bnsClient := bns.New()
	var reply bns.LocalNamingResponse

	err := bnsClient.Call(&bns.LocalNamingRequest{ServiceName: proto.String(bnsName)}, &reply)
	if err != nil {
		ctx.SLog.Warning("GetContainerIDByBns get instance info error").Set("bns", bnsName).Set("backend", backend).SetErr(err).Print()
		return ""
	}

	for _, v := range reply.InstanceInfo {
		// demo instance id: offset.servicename

		ipInt := v.HostIp
		port := v.InstanceStatus.Port
		ipStr := fmt.Sprintf("%d.%d.%d.%d", byte(*ipInt>>24), byte(*ipInt>>16), byte(*ipInt>>8), byte(*ipInt))
		portStr := strconv.Itoa(int(*port))

		instance := ipStr + ":" + portStr
		if instance == backend {
			offset := v.GetOffset()
			serviceName := v.GetServiceName()
			instanceID := fmt.Sprintf("%d.%s", offset, serviceName)
			return instanceID
		}
	}
	// ctx.SLog.Warning("can not find backend in bns").Set("bns", bnsName).Set("backend", backend).Print()
	return ""
}

func GetInstanceIPPort(ctx *easy.Context, instance string) string {
	bnsClient := bns.New()
	var reply bns.LocalNamingResponse

	err := bnsClient.Call(&bns.LocalNamingRequest{ServiceName: proto.String(instance)}, &reply)
	if err != nil {
		ctx.SLog.Warning("GetInstanceIPPort get instance info error").Set("instance", instance).SetErr(err).Print()
		return ""
	}

	if len(reply.InstanceInfo) != 1 {
		ctx.SLog.Warning("instance info len is not one").Set("instance", instance).Set("reply instance info", reply.InstanceInfo).Print()
		return ""
	}

	ipInt := reply.InstanceInfo[0].HostIp
	ipStr := fmt.Sprintf("%d.%d.%d.%d", byte(*ipInt>>24), byte(*ipInt>>16), byte(*ipInt>>8), byte(*ipInt))

	port := reply.InstanceInfo[0].InstanceStatus.Port
	portStr := strconv.Itoa(int(*port))
	return ipStr + ":" + portStr
}

func GetBnsInstanceNum(ctx *easy.Context, bnsName string) int {
	bnsClient := bns.New()
	var reply bns.LocalNamingResponse

	err := bnsClient.Call(&bns.LocalNamingRequest{ServiceName: proto.String(bnsName)}, &reply)
	if err != nil {
		ctx.SLog.Warning("GetBnsInstanceNum get instance info error").Set("bns", bnsName).SetErr(err).Print()
		return 0
	}

	return len(reply.InstanceInfo)
}

func GetBnsInstance(ctx *easy.Context, bnsName string) ([]string, error) {
	var res []string
	var info *bns.LocalNamingResponse
	var err error
	for i := 0; i < 3; i++ {
		if i > 0 {
			// 间隔重试
			duration := i * 50 * int(time.Millisecond)
			time.Sleep(time.Duration(duration))
		}

		info, err = getBnsInstanceDo(ctx, bnsName)
		if err != nil {
			ctx.SLog.Warning("get bns instance do failed").Set("bns", bnsName).Set("try times", i).SetErr(err).Print()
			continue
		}

		resCode := info.GetRetcode()
		if resCode == 0 || resCode == -11 || resCode == -16 || resCode == -1 {
			break
		}
		err = fmt.Errorf("get bns instance failed, bns is %s, res code is %d", bnsName, resCode)
	}

	if err != nil {
		ctx.SLog.Warning("get bns instance failed").Set("bns", bnsName).SetErr(err).Print()
		return nil, err
	}

	instances := info.GetInstanceInfo()
	for _, item := range instances {
		ipInt := item.GetHostIp()
		if ipInt == 0 {
			continue
		}

		ip := fmt.Sprintf("%d.%d.%d.%d", byte(ipInt>>24), byte(ipInt>>16), byte(ipInt>>8), byte(ipInt))
		res = append(res, ip)
	}

	ctx.SLog.Warning("get bns instance success").Set("bns", bnsName).Set("instance num", len(res)).Print()

	if len(res) == 0 {
		ctx.SLog.Warning("get bns instance failed, no valid ip").Set("bns", bnsName).Print()
		return nil, fmt.Errorf("no valid ip")
	}
	return res, nil
}

func getBnsInstanceDo(ctx *easy.Context, bnsName string) (*bns.LocalNamingResponse, error) {
	var reply bns.LocalNamingResponse

	bnsClient := bns.New()
	err := bnsClient.Call(&bns.LocalNamingRequest{ServiceName: proto.String(bnsName)}, &reply)
	if err != nil {
		ctx.SLog.Warning("getBnsInstanceDo get instance info error").Set("bns", bnsName).SetErr(err).Print()
		return nil, err
	}
	return &reply, nil
}

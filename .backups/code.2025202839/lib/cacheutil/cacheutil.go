package cacheutil

import (
	"fmt"
	"math/rand"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

type ContainerInfo struct {
	LastForbidTime int64                            `json:"last_forbidtime"`
	ForbidMachine  map[string](map[string]struct{}) `json:"forbid_machine"`
}

const (
	P3UFCPrefix  = "p-3ufc-beijing-" // 核心数据前缀
	P3UFCSandBox = "p-3ufc-sandbox-yangquan-"

	P6UFCPrefix      = "p-6ufc-" // 辅助数据前缀
	P6UFCSandbox     = "p-6ufc-sandbox-"
	MaxRandomTimeout = 5 * 60 // 随机时间

	// 单位hour，hashtable 过期时间，如果配置超过以下阈值，则设置为Default
	TTLHourMin     = 0
	TTLHourMax     = 720
	TTLHourDefault = 24
)

var (
	ConfigCachePrefix string // p-5ufc-
)

func InitVariable() {
	ConfigCachePrefix = easy.Conf.Cache.RedisPrefix
}

// p-3ufc
func WithP3UFCPrefix(key string) string {
	return P3UFCPrefix + key
}

// 沙盒redis前缀 p-3ufc-sandbox-bejing
func WithP3UFCSandboxPrefix(key string) string {
	return P3UFCSandBox + key
}

func WithConfigCachePrefix(key string) string {
	return ConfigCachePrefix + key
}

// expireTime为固定时间 + 随机时间，避免同时大量hashtable过期，该函数用于生成随机时间
func GenRandomTimeout() int {
	return rand.Intn(MaxRandomTimeout)
}

// ip -> mtime
func GetUFCIPMtimesZSetName() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}

	return prefix + "moniter-ufc-mtime-zset"
}

func GetUFCPrivilegedLatencyZSetName() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}

	return prefix + "moniter-privilege-latency-zset"
}

func GetUFCWorkerLatencyZSetName() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}

	return prefix + "moniter-worker-latency-zset"
}

// 存储延迟信心
func GetWarnOnlineInfoKey() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}
	return prefix + "moniter-online-warn-info"
}

func GetWarnNotOnlineInfoKey() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}
	return prefix + "moniter-not-online-warn-info"
}

func GetVersionHSetName() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}
	return prefix + "version-key"
}

func GetVersionWhiteListSetName() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}

	return prefix + "version-set"
}

func GetUFCVersionName() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}
	return prefix + "agent-version"
}

func GetLastHiWarnInfoKey(isOnline bool) string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}

	tmp := "online"
	if !isOnline {
		tmp = "offline"
	}

	return prefix + "last-latency-" + tmp + "-hi-warn-info"
}

func GetUfcLogRotationHSet(hourTime string) string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}
	return prefix + "log-rotation-hset:" + hourTime
}

func GetLastHour() string {
	return time.Now().Add(-1 * time.Hour).Format("**********")
}

func GetCurHour() string {
	return time.Now().Format("**********")
}

func GetUfcOldVersionHsetName() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}
	return prefix + "agent-old-version-set"
}

func GetAgentLatencyKey(timeSuffix string) string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}

	return prefix + "agent-latency:" + timeSuffix
}

// UFC 配置相关
func GetUfcServicesCnt(prefix string) string {
	return prefix + "services-cnt"
}

func GetUfcServiceIndex(prefix string, index int64) string {
	return fmt.Sprintf("%sservices-%d", prefix, index)
}

func GetUfcServiceIPs(prefix string, name string) string {
	return fmt.Sprintf("%sservices-%s-ips", prefix, name)
}

func GetUfcServiceMtime(prefix string, name string) string {
	return fmt.Sprintf("%sservices-%s-mtime", prefix, name)
}

func RemoveUfcPrefix(prefix string, key string) string {
	return strings.TrimPrefix(key, prefix)
}

func GetIPsLatencyKey() string {
	prefix := P6UFCPrefix
	if !conf.Application.IsOnline {
		prefix = P6UFCSandbox
	}
	return prefix + "ips-latency-key"
}

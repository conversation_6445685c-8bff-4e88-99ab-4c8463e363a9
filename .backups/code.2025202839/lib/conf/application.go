/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 配置定义，热更新实现
 */
package conf

import (
	"errors"
	"sync"

	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
)

// 业务配置
type ApplicationConfMigrate struct {
	IntervalTime                uint32   `json:"intervalTime" `
	HashTableTTL                uint32   `json:"hashTableTTL" `
	ClientIPMaxCntForInterval   uint32   `json:"clientIPMaxCntForInterval" `
	ClientIPCnt                 uint32   `json:"clientIPCnt" `
	TimeStampCnt                uint32   `json:"timeStampCnt" `
	RedisHashTableCntForService uint32   `json:"redisHashTableCntForService" `
	MigrationInstanceTTLDay     uint32   `json:"migrationInstanceTTLDay" `
	MigrationMaxCnt             uint32   `json:"migrationMaxCnt" `
	MigrationEabled             bool     `json:"migrationEabled" `
	WhiteListApp                []string `json:"whiteListApp" `
}

type ApplicationConfHiWarning struct {
	URL               string `json:"url" `
	OpURL             string `json:"opUrl" `
	AgentLatencyURL   string `json:"agentLatencyUrl" `
	FullDiskURL       string `json:"fullDiskUrl" `
	UfcLogRotationURL string `json:"ufcLogRotationUrl" `
	OtherURL          string `json:"otherUrl" `
	AppEnable         bool   `json:"appEnable" `
	NoAppEable        bool   `json:"noAppEable" `
}

type ApplicationConfUfcDeploy struct {
	Enable          bool `json:"enable" `
	DeployThreshold int  `json:"deployThreshold" `
}

type ApplicationConfWatchdogHandleInterval struct {
	UfcLogInterval   int64 `json:"UfcLogInterval" `
	DiskFullInterval int64 `json:"DiskFullInterval" `
	OtherInterval    int64 `json:"OtherInterval" `
}

type ApplicationConfWatchdogHiFilter struct {
	MsgFiltList      []string `json:"msgFiltList" `
	TitleFiltList    []string `json:"titleFiltList" `
	DiskThreshold    float64  `json:"diskThreshold" `
	UfcDiskThreshold int64    `json:"ufcDiskThreshold" `
}

type ApplicationConfWatchdog struct {
	HiEnable         bool                                   `json:"hiEnable" `
	DiskFuncHiEnable bool                                   `json:"DiskFuncHiEnable" `
	UfcLogHiEnable   bool                                   `json:"UfcLogHiEnable" `
	HandleInterval   *ApplicationConfWatchdogHandleInterval `json:"handleInterval" `
	HiFilter         *ApplicationConfWatchdogHiFilter       `json:"hiFilter" `
	HiAtOpEnable     bool                                   `json:"hiAtOpEnable" `
}

type ApplicationConfLatency struct {
	HiEnable                  bool `json:"hiEnable" `
	PrivilegeLatencyThreshold int  `json:"privilegeLatencyThreshold" `
	CheckMachineCnt           int  `json:"checkMachineCnt" `
	HeartbeatEnable           bool `json:"heartbeatEnable" `
}

type ApplicationConfSmallflowCheck struct {
	Timeout int64 `json:"timeout" `
	Batch   int   `json:"batch" `
}

type ApplicationConf struct {
	Mu                     sync.RWMutex
	fileName               string
	fileType               tangram_config.Decoder
	Migrate                *ApplicationConfMigrate        `json:"migrate" `
	HiWarning              *ApplicationConfHiWarning      `json:"hiWarning" `
	UfcDeploy              *ApplicationConfUfcDeploy      `json:"ufcDeploy" `
	Watchdog               *ApplicationConfWatchdog       `json:"watchdog" `
	Latency                *ApplicationConfLatency        `json:"latency" `
	IsOnline               bool                           `json:"isOnline" `
	ConfigUpdateStatEnable bool                           `json:"configUpdateStatEnable" `
	EnableInPerRequest     bool                           `json:"enableInPerRequest" `
	EnableInTimer          bool                           `json:"enableInTimer" `
	DeleteZset             bool                           `json:"deleteZset" `
	UfcBns                 []string                       `json:"ufcBns" `
	UfcKeyRightCheckPrefix []string                       `json:"ufcKeyRightCheckPrefix" `
	UploadVersionEnable    bool                           `json:"uploadVersionEnable" `
	SmallflowCheck         *ApplicationConfSmallflowCheck `json:"smallflowCheck" `
}

// 热更新Fetch
func (c *ApplicationConf) Fetch(data []byte) error {
	c.Mu.Lock()
	defer c.Mu.Unlock()
	if len(data) > 0 {
		err := tangram_config.Unmarshal(c.fileType, data, c)
		if err != nil {
			tangram_logger.Error("[msg:conf Fetch error] [filename: %s] [error: %v]", c.fileName, err)
			return err
		}
	} else {
		tangram_logger.Warning("[msg:conf Fetch error] [filename: %s] [error: data is empty]", c.fileName)
		return errors.New("data is empty")
	}
	return nil
}

/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 配置单测
 */

package conf

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
)

// 测试配置文件是否获取到
func TestConfAll(t *testing.T) {
	config_mock.SetConfRootPath("../../conf")
	Init()
	// 业务配置
	tangram_logger.Info("Application %+v", Application)
	assert.NotNil(t, Application)

}

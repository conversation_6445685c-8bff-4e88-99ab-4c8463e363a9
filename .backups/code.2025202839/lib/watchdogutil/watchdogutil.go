package watchdogutil

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/entity/remote"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

// AlertLevel 告警级别
type AlertLevel = string

const (
	AlertLevelInfo     AlertLevel = "info"
	AlertLevelWarn     AlertLevel = "warn"
	AlertLevelError    AlertLevel = "error"
	AlertLevelCritical AlertLevel = "critical"
)

// AlertType 告警类型
type AlertType = string

const (
	AlertTypeNginxLog AlertType = "nginx_log"
	AlertTypeDisk     AlertType = "disk"
	AlertTypeOther    AlertType = "other"
)

type WatchDogAlert struct {
	Type      AlertType              `json:"type"`
	Level     AlertLevel             `json:"level"`
	Title     string                 `json:"title"`
	Message   string                 `json:"message"`
	Timestamp time.Time              `json:"timestamp"`
	Hostname  string                 `json:"hostname"`
	Details   map[string]interface{} `json:"details,omitempty"`
}

func GetBytesByMessage(errorMsg string) (uint64, error) {
	formattedSize, err := extractFormattedSize(errorMsg)
	if err != nil {
		return 0, err
	}

	bytes, err := parseBytes(formattedSize)
	if err != nil {
		return 0, err
	}
	return bytes, nil
}

func extractFormattedSize(errorMsg string) (string, error) {
	// consecutive failures: 577, error: latest log file too large: /home/<USER>/nginx_ufc/logs/access.log.2025062515 (10.10 GB > 10.00 GB)

	// 查找第一个 '(' 和第一个 ')' 的位置
	start := strings.Index(errorMsg, "(")
	end := strings.Index(errorMsg, ")")
	if start == -1 || end == -1 || start >= end {
		return "", fmt.Errorf("invalid error message format")
	}

	// 提取括号内的内容并分割
	content := strings.TrimSpace(errorMsg[start+1 : end])
	parts := strings.Split(content, ">")
	if len(parts) < 2 {
		return "", fmt.Errorf("invalid size comparison format")
	}

	// 提取第一个大小值并去除前后空格
	sizeStr := strings.TrimSpace(parts[0])
	return sizeStr, nil
}

func parseBytes(s string) (uint64, error) {
	// 分割数值和单位
	var num float64
	var unit string
	_, err := fmt.Sscanf(s, "%f %s", &num, &unit)
	if err != nil {
		return 0, fmt.Errorf("invalid format: %w", err)
	}

	// 定义单位映射
	units := map[string]uint64{
		"B":  1,
		"KB": 1024,
		"MB": 1024 * 1024,
		"GB": 1024 * 1024 * 1024,
		"TB": 1024 * 1024 * 1024 * 1024,
		"PB": 1024 * 1024 * 1024 * 1024 * 1024,
	}

	multiplier, ok := units[unit]
	if !ok {
		return 0, fmt.Errorf("unknown unit: %s", unit)
	}

	return uint64(num * float64(multiplier)), nil
}

func IsFilterMsg(alertInfo *WatchDogAlert) bool {
	msg := alertInfo.Message
	msgFiltList := conf.Application.Watchdog.HiFilter.MsgFiltList
	for _, item := range msgFiltList {
		if strings.Contains(msg, item) {
			return true
		}
	}

	title := alertInfo.Title
	titleFiltList := conf.Application.Watchdog.HiFilter.TitleFiltList
	for _, item := range titleFiltList {
		if strings.Contains(title, item) {
			return true
		}
	}

	// 磁盘满时过滤
	usedPercent, ok := alertInfo.Details["used_percent"]
	if ok {
		usedPercentFloat64 := conv.ToFloat64(usedPercent)
		if usedPercentFloat64 < conf.Application.Watchdog.HiFilter.DiskThreshold {
			return true
		}
	}

	bytes, err := GetBytesByMessage(msg)
	if err == nil {
		if bytes < uint64(conf.Application.Watchdog.HiFilter.UfcDiskThreshold) {
			return true
		}
	}

	return false
}

func GetZhibanOPList(ctx *easy.Context) ([]string, error) {
	zhibanClient := remote.NewZhiBanClient(ctx)
	body := &remote.ZhiBanPost{
		Token:    "MuojeZiiW0MUzG2eWTgIHdbF95Grl7wg",
		ZhibanID: "61971",
	}

	err := zhibanClient.Do(body)
	if err != nil {
		ctx.SLog.Error("alert zhiban error").SetErr(err).Print()
		return nil, err
	}

	var m map[string]interface{}
	if err := json.Unmarshal(zhibanClient.Response.Body, &m); err != nil {
		ctx.SLog.Error("alert zhiban error").SetErr(err).Print()
		return nil, err
	}

	dataSlice, err := conv.ToSliceE(m["data"])
	if err != nil {
		ctx.SLog.Error("alert zhiban errr type not match").SetErr(err).Print()
		return nil, err
	}

	var userList []string
	for _, data := range dataSlice {
		dataMap, ok := data.(map[string]interface{})
		if !ok {
			ctx.SLog.Error("alert zhiban error").SetErr(err).Print()
			return nil, err
		}
		dataUserList, err := conv.ToStringSliceE(dataMap["dutyUserList"])
		if err != nil {
			ctx.SLog.Warning("alert zhiban error").SetErr(err).Print()
			return nil, err
		}

		userList = append(userList, dataUserList...)
	}
	return userList, nil

}

func IsHiEnable(typ AlertType) bool {
	if typ == AlertTypeNginxLog {
		return conf.Application.Watchdog.HiEnable && conf.Application.Watchdog.UfcLogHiEnable
	}

	if typ == AlertTypeDisk {
		return conf.Application.Watchdog.HiEnable && conf.Application.Watchdog.DiskFuncHiEnable
	}

	return conf.Application.Watchdog.HiEnable
}

func GetSelfMatrixHost() string {
	hostIP := os.Getenv("MATRIX_HOST_IP")
	port := os.Getenv("MATRIX_INSTANCE_PORTS")

	slice := strings.Split(port, ",")
	for _, item := range slice {
		if !strings.Contains(item, "main") || !strings.Contains(item, "=") {
			continue
		}

		itemSlice := strings.Split(item, "=")
		if len(itemSlice) <= 1 {
			continue
		}
		return fmt.Sprintf("%s:%s", hostIP, itemSlice[1])
	}

	return ""
}

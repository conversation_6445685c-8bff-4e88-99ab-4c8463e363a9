package worker

import (
	"bytes"
	"fmt"
	"html/template"
	"net/smtp"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/hashutil"
)

var MailFrom = "<EMAIL>"
var MailTo = []string{"<EMAIL>"}

type SendMailWorker struct {
	Context                       *easy.Context
	LastDayForbidInstances        []string // 昨天 ban 的实例
	LastHourForbidSuccessInstance []string
	LastHourForbidFailedInstance  []string
	LastHourForbidSuccessLen      int
	LastHourForbidFailedLen       int

	MatrixName string
}

func NewSendMailWorker(ctx *easy.Context) *SendMailWorker {
	return &SendMailWorker{
		Context:    ctx,
		MatrixName: getMatrixID(),
	}
}

// 收集的信息包括:
// 表1: 每天的封禁实例信息。
// 表2: 每小时收集到的hashtable名称。以及该小时内, hashset所用到了哪些hashtable, 供后续手动去获取该 hashtable 所存放的信息
func (s *SendMailScheduler) Handler() {
	ctx := easy.NewContext()

	ctx.SLog.Info("--------handler").Print()

	redis := easy.NewRedis(ctx, "redis")

	curHashSetForBackend := hashutil.GetLastDayHashTableForBackendName()
	realHashSet := cacheutil.WithConfigCachePrefix(curHashSetForBackend)

	members, err := redis.SMembers(realHashSet)
	if err != nil {
		ctx.SLog.Warning("send mail worker get redis members error").Set("hashset", realHashSet).SetErr(err).Print()
		return
	}

	sendMailworker := NewSendMailWorker(ctx)
	sendMailworker.LastDayForbidInstances = members
	timePrefix := time.Now().Add(-24 * time.Hour).Format("20060102")
	for i := 0; i < 24; i++ {
		var date string
		if i < 10 {
			date = fmt.Sprintf("%s0%d", timePrefix, i)
		} else {
			date = fmt.Sprintf("%s%d", timePrefix, i)
		}

		hashSetName := hashutil.GetHashSetName(date) // hashset 装该时段用到了哪些 hashtable
		realHashSet := cacheutil.WithConfigCachePrefix(hashSetName)
		hashTableNames, err := redis.SMembers(realHashSet)
		if err != nil {
			ctx.SLog.Warning("send mail worker get redis members error").Set("hashset", hashSetName).Print()
			continue
		}
		// 这个打日志记录就好，发报表数量太多了
		ctx.SLog.Info("stat hashtable names").Set("set name", realHashSet).Set("hashtable", hashTableNames).Print()
	}

	if err := sendMailworker.SendToMailDaily(); err != nil {
		ctx.SLog.Error("worker: send mail task failed").SetErr(err).Print()
		return
	}
}

// 天粒度
func (worker *SendMailWorker) SendToMailDaily() error {
	t, err := template.New("tableTmpl").Funcs(template.FuncMap{
		"inc": func(i int) int {
			return i + 1
		},
	}).Parse(tableTmpl)
	if err != nil {
		worker.Context.SLog.Error("template parse failed").SetErr(err).Print()
		return err
	}

	var buffer bytes.Buffer
	if err := t.Execute(&buffer, worker); err != nil {
		worker.Context.SLog.Error("template execute failed").SetErr(err).Print()
		return err
	}

	subject := "UFC封禁实例迁移记录报表"
	msg := buffer.String()

	if err := smtp.SendMail(
		"mail2-in.baidu.com:25", // 内网邮件服务器
		nil,                     // 内网邮件服务器支持匿名SMTP
		MailFrom,                // 发信人
		MailTo,                  // 收信人
		[]byte(strings.Join([]string{
			"FROM: <EMAIL>", // 邮件客户端显示的发信人
			"TO: <EMAIL>",   // 邮件客户端显示的收信人
			"SUBJECT: " + subject,          // 标题
			"Content-Type: text/html; charset=UTF-8;",
			"",  // 邮件头和正文之间要有一个空行
			msg, // 正文
		}, "\r\n")), // RFC协议规定的换行符是\r\n
	); err != nil {
		worker.Context.SLog.Error("send mail fail").SetErr(err).Print()
		return err
	}

	worker.Context.SLog.Info("send mail success").Print()
	return nil
}

// 小时粒度
func (worker *SendMailWorker) SendToMailHourly(successList []string, failedList []string) error {
	worker.LastHourForbidSuccessInstance = successList
	worker.LastHourForbidSuccessLen = len(successList)

	worker.LastHourForbidFailedInstance = failedList
	worker.LastHourForbidFailedLen = len(failedList)

	t, err := template.New("tableTmplHourly").Funcs(template.FuncMap{
		"inc": func(i int) int {
			return i + 1
		},
	}).Parse(tableTmplHourly)
	if err != nil {
		worker.Context.SLog.Error("template parse failed").SetErr(err).Print()
		return err
	}

	var buffer bytes.Buffer
	if err := t.Execute(&buffer, worker); err != nil {
		worker.Context.SLog.Error("template execute failed").SetErr(err).Print()
		return err
	}

	subject := "UFC封禁实例迁移记录报表(小时粒度)"
	msg := buffer.String()

	if err := smtp.SendMail(
		"mail2-in.baidu.com:25", // 内网邮件服务器
		nil,                     // 内网邮件服务器支持匿名SMTP
		MailFrom,                // 发信人
		MailTo,                  // 收信人
		[]byte(strings.Join([]string{
			"FROM: <EMAIL>", // 邮件客户端显示的发信人
			"TO: <EMAIL>",   // 邮件客户端显示的收信人
			"SUBJECT: " + subject,          // 标题
			"Content-Type: text/html; charset=UTF-8;",
			"",  // 邮件头和正文之间要有一个空行
			msg, // 正文
		}, "\r\n")), // RFC协议规定的换行符是\r\n
	); err != nil {
		worker.Context.SLog.Error("send mail fail").SetErr(err).Print()
		return err
	}

	worker.Context.SLog.Info("send mail success").Print()
	return nil
}

const tableTmpl = `
<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
	</head>
	<body>
	
	昨天封禁实例信息
	<table border="1">
	<tr>
		<th>序号</th>
		<th>实例信息</th>
	</tr>
	{{ range $index, $info := .LastDayForbidInstances }}
		<td>{{$index | inc}}</td>
		<td>{{$info}}</td>
	</tr>
	{{end}}	
  	</table>

	<br />
	<br />
  
	</body>
</html>
`

const tableTmplHourly = `
<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
	</head>
	<body>

	<table border="1">
	<tr>
		<th>成功迁移节点数</th>
		<th>失败迁移节点数</th>
		<th>执行定时任务的实例</th> 
	</tr>
		<td>{{ .LastHourForbidSuccessLen }}</td>
		<td>{{ .LastHourForbidFailedLen }}</td>
		<td>{{ .MatrixName}}</td>
	</tr>
  	</table>

	<br />
	<br />
	
	上一个小时的封禁信息(成功迁移节点）
	<table border="1">
	<tr>
		<th>序号</th>
		<td>实例信息</td>
	</tr>
	{{ range $index, $info := .LastHourForbidSuccessInstance }}
		<td>{{$index | inc}}</td>
		<td>{{$info}}</td>
	</tr>
	{{end}}	
  	</table>

	<br />
	<br />

	上一个小时的封禁信息(失败迁移节点）
		<table border="1">
		<tr>
			<th>序号</th>
			<th>实例信息</th>
		</tr>
		{{ range $index, $info := .LastHourForbidFailedInstance }}
			<td>{{$index | inc}}</td>
			<td>{{$info}}</td>
		</tr>
		{{end}}	
		</table>
	<br />
	<br />
	</body>
</html>
`

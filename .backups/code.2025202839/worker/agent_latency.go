package worker

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"sync"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/utils/conv"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/deployutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/netutil"
)

type WarnInfo struct {
	Matrix          string           `json:"matrix"`
	IsSandboxUfc    bool             `json:"是否为沙盒服务"`
	UpdateFailedIPs []string         `json:"特权进程更新失败,omitempty"`  // 特权进程一直没有更新
	UpdateDelayIPs  map[string]int64 `json:"特权进程更新延迟,omitempty"`  // 特权进程更新延迟
	NotInBnsIPs     []string         `json:"-"`                   //`json:"有UFC但不在BNS中,omitempty"` // 在特权进程中存在，但不在BNS中的IP列表
	NotUploadIPs    []string         `json:"在BNS中但未上报,omitempty"` // 在BNS中存在，但在特权进程中不存在的IP列表
	NotNewUfcIPs    []string         `json:"-"`                   // 在BNS中存在，但在特权进程中不存在的IP列表
	Message         string           `json:"message"`

	Time int64 `json:"mtime"`
}

type HiVersionInfo struct {
	NewVersion string      `json:"new_version"`
	IPVersions []IPVersion `json:"ip_versions"`
}

type IPVersion struct {
	IP      string `json:"ip"`
	Version string `json:"version"`
}

func InitWarnInfo(
	updateFailedIPMap map[string]struct{},
	updateDelayIPMap map[string]int64,
	notInBnsIPMap map[string]struct{},
	notNewUfcIPMap map[string]struct{},
	notUploadIPMap map[string]struct{}) (*WarnInfo, error) {
	name := os.Getenv("MATRIX_INSTANCE_ID")
	if name == "" {
		var err error
		name, err = os.Hostname()
		if err != nil {
			return nil, err
		}
	}

	updatedFailedIPs := mapToSlice(updateFailedIPMap)
	notInBnsIPs := mapToSlice(notInBnsIPMap)
	notUploadIPs := mapToSlice(notUploadIPMap)
	notNewUfcIPs := mapToSlice(notNewUfcIPMap)

	res := &WarnInfo{}
	hasInfo := false // 有实际消息上报
	if len(updatedFailedIPs) != 0 {
		res.UpdateFailedIPs = updatedFailedIPs
		hasInfo = true
	}
	if len(updateDelayIPMap) != 0 { // 上报IP
		res.UpdateDelayIPs = updateDelayIPMap
		hasInfo = true
	}
	if len(notInBnsIPs) != 0 {
		res.NotInBnsIPs = notInBnsIPs
		hasInfo = true
	}

	if len(notNewUfcIPs) != 0 {
		res.NotNewUfcIPs = notNewUfcIPs
		hasInfo = true
	}

	if len(notUploadIPs) != 0 {
		res.NotUploadIPs = notUploadIPs
		hasInfo = true

	}
	res.Time = time.Now().Unix() // 用于记录Redis
	res.Matrix = name
	res.IsSandboxUfc = !conf.Application.IsOnline
	if !hasInfo {
		res.Message = "no upload"
	}
	return res, nil
}

func mapToSlice(m map[string]struct{}) []string {
	slice := make([]string, 0)
	if len(m) == 0 {
		return slice
	}

	for k := range m {
		slice = append(slice, k)
	}
	sort.Slice(slice, func(i, j int) bool {
		return slice[i] < slice[j]
	})
	return slice
}

// 定时任务处理的handler
// 获取在 BNS 中，但没有数据上报（上次数据上报时间过久） 的 IP, HI报警
func (s *AgentLatencyScheduler) Handler() {
	ctx := easy.NewContext()
	// 结构化日志打印，详情可参见https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	ctx.SLog.Info("AgentLatencyScheduler --------handler").Print()

	ufcBnsIPs := make(map[string]struct{}) // UFC BNS下的机器IP列表
	// 获取BNS下的IP列表
	var bnsList []string = conf.Application.UfcBns
	for _, bns := range bnsList {
		// 内部做了重试
		ips, err := common.GetBnsInstance(ctx, bns)
		if err != nil {
			ctx.SLog.Warning("get bns instance error").Set("bns", bns).Print()
			continue
		}

		for _, ip := range ips {
			ufcBnsIPs[ip] = struct{}{}
		}
	}
	ctx.SLog.Info("ufc bns ips info").Set("len", len(ufcBnsIPs)).Print()

	if conf.Application.IsOnline && conf.Application.UploadVersionEnable {
		if err := s.versionDiffCheck(ctx, ufcBnsIPs); err != nil {
			ctx.SLog.Warning("upload version error").SetErr(err).Print()
		} else {
			ctx.SLog.Info("upload ufc version success").Print()
		}
	}

	zsetName := cacheutil.GetUFCIPMtimesZSetName()
	redis := easy.NewRedis(ctx, "redis")

	slice, err := redis.ZRevRangeWithScores(zsetName, 0, -1)
	if err != nil {
		ctx.SLog.Warning("redis zrange error").SetErr(err).Print()
		return
	}

	if len(slice)%2 == 1 {
		ctx.SLog.Warning("redis zrange witch scores failed").Set("name", zsetName).Set("len slice", len(slice)).Print()
		return
	}

	updateFailedIPs := make(map[string]struct{}) // 上报过，但 mtime 一直没有更新成功，为默认值 -1
	updateDelayIPs := make(map[string]int64)     // 存在数据上报，但已经延迟的 IP 列表

	uploadIPs := make(map[string]struct{}) // 有数据上报的IP集合

	notInBnsIPs := make(map[string]struct{})  // 存在数据上报，但不在BNS的IP列表中
	notUploadIPs := make(map[string]struct{}) // 在BNS IP列表中，但是没有数据上报的IP列表
	notNewUfcIPs := make(map[string]struct{})
	timeNowUnix := time.Now().Unix()

	// 记集合 A 为有数据上报的 IP集合, 集合 B 为 BNS UFC下的IP集合 ufcBnsIPs
	// 需要计算 A - B 和 B - A 来 Hi 报警处理
	resLen := len(slice) / 2
	for i := 0; i < resLen; i++ {
		ip := slice[2*i]
		timstamp := conv.ToInt(slice[2*i+1])
		if timstamp == -1 {
			// 特权进程有问题
			updateFailedIPs[ip] = struct{}{}
		} else if timstamp == 0 {
			// 业务机器未升级最新版UFC
			notNewUfcIPs[ip] = struct{}{}
		} else {
			latency := timeNowUnix - int64(timstamp)
			if latency > 30*60 {
				// 超过半小时没更新则收集起来Hi报警
				ctx.SLog.Warning("update delay ip").Set("ip", ip).Set("timeNow", timeNowUnix).Set("timestamp", timstamp).Set("latency", latency).Print()
				updateDelayIPs[ip] = latency
			}

		}

		uploadIPs[ip] = struct{}{}

		if _, ok := ufcBnsIPs[ip]; !ok {
			// 维护有上报，但不在BNS IP列表中
			notInBnsIPs[ip] = struct{}{}
		}
	}

	for ip := range ufcBnsIPs {
		if _, ok := uploadIPs[ip]; !ok {
			notUploadIPs[ip] = struct{}{}
		}
	}
	ctx.SLog.Warning("not upload ips").Set("len", len(notUploadIPs)).Print()

	// 组装数据 Hi 报警
	warnInfo, err := InitWarnInfo(updateFailedIPs, updateDelayIPs, notInBnsIPs, notNewUfcIPs, notUploadIPs)
	if err != nil {
		ctx.SLog.Warning("init warn info error").Set("error", err).Print()
		return
	}

	onlineWarnInfo, notOnlineWarnInfo := s.splitWarnInfo(warnInfo)

	// 机器上存在 opera 实例时，是否开启hi报警
	if conf.Application.HiWarning.AppEnable {
		onlineWarnInfoByte, err := json.Marshal(onlineWarnInfo)
		if err != nil {
			ctx.SLog.Warning("marshal faield").SetErr(err).Print()
			return
		}
		onlineWarnInfoStr := string(onlineWarnInfoByte)

		var onlineHiInfo string
		if len(onlineWarnInfoStr) > 1800 {
			onlineWarnInfoStr = onlineWarnInfoStr[1:1800]
		}

		onlineHiInfo = fmt.Sprintf("【是否为沙盒上报: %v】【含opera/orp实例】 \n端到端延迟报警信息: %s\n", warnInfo.IsSandboxUfc, onlineWarnInfoStr)

		sendHiOption := common.SendHiOption{
			Type:    common.SendHiTypeText,
			Content: onlineHiInfo,
		}
		if err := common.SendHi(conf.Application.HiWarning.URL, sendHiOption); err != nil {
			ctx.SLog.Warning("agent latency send hi failed").Set("has_opera", true).Set("url", conf.Application.HiWarning.URL).SetErr(err).Print()
		} else {
			ctx.SLog.Warning("agent latency send hi success").Set("has_opera", true).Set("url", conf.Application.HiWarning.URL).Print()
		}

		// 上报增量信息
		s.sendDiffToHi(ctx, redis, true, onlineWarnInfo)

		// 只针对有Opera/ORP实例且是线上机器，进行自动化部署
		if conf.Application.IsOnline {
			// s.autoDeploy(ctx, onlineWarnInfo)
			deployThreshold := 30
			if conf.Application.UfcDeploy.DeployThreshold != 0 {
				deployThreshold = conf.Application.UfcDeploy.DeployThreshold
			}

			ips := onlineWarnInfo.NotUploadIPs
			if len(onlineWarnInfo.NotUploadIPs) >= deployThreshold {
				ips = onlineWarnInfo.NotUploadIPs[:deployThreshold]
			}

			enable := conf.Application.UfcDeploy.Enable
			ctx.SLog.Info("auto deploy info").Set("threshold", ips).Set("ips", ips).Set("enable", enable).Print()
			if enable {
				if err := deployutil.UfcDeploy(ctx, ips); err != nil {
					ctx.SLog.Warning("ufc auto deploy failed").SetErr(err).Print()
				} else {
					ctx.SLog.Info("ufc auto deploy success").Print()
				}
			}

		}

	}

	// 机器上不存在 opera 实例时，是否开启报警
	if conf.Application.HiWarning.NoAppEable {
		notOnlineWarnInfoByte, err := json.Marshal(notOnlineWarnInfo)
		if err != nil {
			ctx.SLog.Warning("marshal faield").SetErr(err).Print()
			return
		}
		notOnlineWarnInfoStr := string(notOnlineWarnInfoByte)

		var notOnlineHiInfo string
		if len(notOnlineWarnInfoStr) > 1800 {
			notOnlineWarnInfoStr = notOnlineWarnInfoStr[1:1800]
		}
		notOnlineHiInfo = fmt.Sprintf("【是否为沙盒上报: %v】【不含opera/orp实例】\n端到端延迟报警信息: %s\n", warnInfo.IsSandboxUfc, notOnlineWarnInfoStr)

		sendHiOption := common.SendHiOption{
			Type:    common.SendHiTypeText,
			Content: notOnlineHiInfo,
		}

		if err := common.SendHi(conf.Application.HiWarning.URL, sendHiOption); err != nil {
			ctx.SLog.Warning("agent latency send hi failed").Set("has_opera", false).Set("url", conf.Application.HiWarning.URL).SetErr(err).Print()
		} else {
			ctx.SLog.Warning("agent latency send hi success").Set("has_opera", false).Set("url", conf.Application.HiWarning.URL).Print()
		}

		// 上报增量信息
		s.sendDiffToHi(ctx, redis, false, notOnlineWarnInfo)
	}
	// 写 redis
	onlineWarnHostNameInfo := s.getHostnameWarnInfo(*onlineWarnInfo)
	notOnlineWarnHostnameInfo := s.getHostnameWarnInfo(*notOnlineWarnInfo)

	onlineWarnHostNameInfoByte, err := json.Marshal(onlineWarnHostNameInfo)
	if err != nil {
		ctx.SLog.Warning("marshal faield").SetErr(err).Print()
	} else {
		onlineRedisKey := cacheutil.GetWarnOnlineInfoKey()
		if err := redis.Set(onlineRedisKey, string(onlineWarnHostNameInfoByte)); err != nil {
			ctx.SLog.Warning("redis set error").SetErr(err).Print()
		}
	}

	notOnlineWarnHostNameInfoByte, err := json.Marshal(notOnlineWarnHostnameInfo)
	if err != nil {
		ctx.SLog.Warning("marshal faield").SetErr(err).Print()
	} else {
		notOnlineRedisKey := cacheutil.GetWarnNotOnlineInfoKey()
		if err := redis.Set(notOnlineRedisKey, string(notOnlineWarnHostNameInfoByte)); err != nil {
			ctx.SLog.Warning("redis set error").SetErr(err).Print()
		}

	}

	// 删除key
	if conf.Application.DeleteZset {
		privilegedZSet := cacheutil.GetUFCPrivilegedLatencyZSetName()
		workerZSet := cacheutil.GetUFCWorkerLatencyZSetName()
		mtimeZSet := cacheutil.GetUFCIPMtimesZSetName()
		if err := redis.Del(privilegedZSet); err != nil {
			ctx.SLog.Warning("redis del privilegedZSet error").Set("key", privilegedZSet).SetErr(err).Print()
		}
		if err := redis.Del(workerZSet); err != nil {
			ctx.SLog.Warning("redis del workerZSet error").Set("key", workerZSet).SetErr(err).Print()
		}
		if err := redis.Del(mtimeZSet); err != nil {
			ctx.SLog.Warning("redis del mtimeZSet error").Set("key", mtimeZSet).SetErr(err).Print()
		}
	}
}

func (s *AgentLatencyScheduler) splitWarnInfo(warnInfo *WarnInfo) (*WarnInfo, *WarnInfo) {
	onlineWarnInfo := &WarnInfo{
		UpdateDelayIPs: make(map[string]int64),
	}
	onlineWarnInfo.Time = warnInfo.Time
	onlineWarnInfo.IsSandboxUfc = warnInfo.IsSandboxUfc
	onlineWarnInfo.Matrix = warnInfo.Matrix
	onlineWarnInfo.Message = warnInfo.Message
	notOnlineWarnInfo := &WarnInfo{
		UpdateDelayIPs: make(map[string]int64),
	}
	ctx := easy.NewContext()

	wg := easy.NewWaitGroup(ctx, 30)
	var mu sync.Mutex

	for _, item := range warnInfo.NotInBnsIPs {
		wg.Go(func(ctx *easy.Context) {
			ok := netutil.IsOnlineInstance(ctx, item)
			mu.Lock()
			if ok {
				onlineWarnInfo.NotInBnsIPs = append(onlineWarnInfo.NotInBnsIPs, item)
			} else {
				notOnlineWarnInfo.NotInBnsIPs = append(notOnlineWarnInfo.NotInBnsIPs, item)
			}
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for _, item := range warnInfo.NotUploadIPs {
		wg.Go(func(ctx *easy.Context) {
			ok := netutil.IsOnlineInstance(ctx, item)
			mu.Lock()
			if ok {
				onlineWarnInfo.NotUploadIPs = append(onlineWarnInfo.NotUploadIPs, item)
			} else {
				notOnlineWarnInfo.NotUploadIPs = append(notOnlineWarnInfo.NotUploadIPs, item)
			}
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for item := range warnInfo.UpdateDelayIPs {
		wg.Go(func(ctx *easy.Context) {
			ok := netutil.IsOnlineInstance(ctx, item)
			mu.Lock()
			if ok {
				onlineWarnInfo.UpdateDelayIPs[item] = warnInfo.UpdateDelayIPs[item]
			} else {
				notOnlineWarnInfo.UpdateDelayIPs[item] = warnInfo.UpdateDelayIPs[item]
			}
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for _, item := range warnInfo.UpdateFailedIPs {
		wg.Go(func(ctx *easy.Context) {
			ok := netutil.IsOnlineInstance(ctx, item)
			mu.Lock()
			if ok {
				onlineWarnInfo.NotUploadIPs = append(onlineWarnInfo.NotUploadIPs, item)
			} else {
				notOnlineWarnInfo.NotUploadIPs = append(notOnlineWarnInfo.NotUploadIPs, item)
			}
			mu.Unlock()
		})
	}
	wg.WaitAll()
	return onlineWarnInfo, notOnlineWarnInfo

}

func (s *AgentLatencyScheduler) getHostnameWarnInfo(warnInfo WarnInfo) WarnInfo {
	ctx := easy.NewContext()

	wg := easy.NewWaitGroup(ctx, 30)
	res := WarnInfo{
		UpdateDelayIPs: make(map[string]int64),
	}
	res.Time = warnInfo.Time
	res.IsSandboxUfc = warnInfo.IsSandboxUfc
	res.Matrix = warnInfo.Matrix
	res.Message = warnInfo.Message

	var mu sync.Mutex

	for _, item := range warnInfo.NotInBnsIPs {
		wg.Go(func(ctx *easy.Context) {
			hostname := netutil.GetHostnameByIP(ctx, item)
			mu.Lock()
			res.NotInBnsIPs = append(res.NotInBnsIPs, hostname)
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for _, item := range warnInfo.NotUploadIPs {
		wg.Go(func(ctx *easy.Context) {
			hostname := netutil.GetHostnameByIP(ctx, item)
			mu.Lock()
			res.NotUploadIPs = append(res.NotUploadIPs, hostname)
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for item := range warnInfo.UpdateDelayIPs {
		wg.Go(func(ctx *easy.Context) {
			hostname := netutil.GetHostnameByIP(ctx, item)
			mu.Lock()
			res.UpdateDelayIPs[hostname] = res.UpdateDelayIPs[item]
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for _, item := range warnInfo.UpdateFailedIPs {
		wg.Go(func(ctx *easy.Context) {
			hostname := netutil.GetHostnameByIP(ctx, item)
			mu.Lock()
			res.UpdateFailedIPs = append(res.UpdateFailedIPs, hostname)
			mu.Unlock()

		})
	}
	wg.WaitAll()

	return res
}

func (s *AgentLatencyScheduler) GetWarnInfoByHostnameWarnInfo(hostnameWarnInfo WarnInfo) WarnInfo {
	ctx := easy.NewContext()

	wg := easy.NewWaitGroup(ctx, 30)
	res := WarnInfo{
		UpdateDelayIPs: make(map[string]int64),
	}
	res.Time = hostnameWarnInfo.Time
	res.IsSandboxUfc = hostnameWarnInfo.IsSandboxUfc
	res.Matrix = hostnameWarnInfo.Matrix
	res.Message = hostnameWarnInfo.Message

	var mu sync.Mutex

	for _, item := range hostnameWarnInfo.NotInBnsIPs {
		wg.Go(func(ctx *easy.Context) {
			hostname := netutil.GetIPByHostname(ctx, item)
			mu.Lock()
			res.NotInBnsIPs = append(res.NotInBnsIPs, hostname)
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for _, item := range hostnameWarnInfo.NotUploadIPs {
		wg.Go(func(ctx *easy.Context) {
			hostname := netutil.GetIPByHostname(ctx, item)
			mu.Lock()
			res.NotUploadIPs = append(res.NotUploadIPs, hostname)
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for item := range hostnameWarnInfo.UpdateDelayIPs {
		wg.Go(func(ctx *easy.Context) {
			hostname := netutil.GetIPByHostname(ctx, item)
			mu.Lock()
			res.UpdateDelayIPs[hostname] = res.UpdateDelayIPs[item]
			mu.Unlock()
		})
	}
	wg.WaitAll()

	for _, item := range hostnameWarnInfo.UpdateFailedIPs {
		wg.Go(func(ctx *easy.Context) {
			hostname := netutil.GetIPByHostname(ctx, item)
			mu.Lock()
			res.UpdateFailedIPs = append(res.UpdateFailedIPs, hostname)
			mu.Unlock()

		})
	}
	wg.WaitAll()

	return res
}

func (s *AgentLatencyScheduler) versionDiffCheck(ctx *easy.Context, bnsIPs map[string]struct{}) error {
	redis := easy.NewRedis(ctx, "redis")

	// 获取 ufc 版本对应的 ip 列表
	hsetName := cacheutil.GetVersionHSetName()
	ipVersions, err := redis.HgetallReturnMap(hsetName)
	if err != nil {
		ctx.SLog.Warning("ufc versions hgetall failed").Set("hset name", hsetName).SetErr(err).Print()
		return err
	}

	// 获取 ufc 最新版本
	versionKey := cacheutil.GetUFCVersionName()
	version, err := redis.Get(versionKey)
	if err != nil {
		ctx.SLog.Warning("ufc version get failed").Set("version key", versionKey).SetErr(err).Print()
		return err
	}

	// 获取 ufc 白名单版本
	whiteListKey := cacheutil.GetVersionWhiteListSetName()
	whiteList, err := redis.SMembers(whiteListKey)
	if err != nil && !assert.IsRedisErrNil(err) {
		ctx.SLog.Warning("ufc version white list get failed").Set("white list key", whiteListKey).SetErr(err).Print()
		return err
	}
	whiteListSet := make(map[string]struct{})
	for _, ip := range whiteList {
		whiteListSet[ip] = struct{}{}
	}

	var tmpIPVersionSlice []IPVersion
	for ip, ipVersion := range ipVersions {
		if ipVersion == version {
			continue
		}

		if _, ok := whiteListSet[ip]; ok {
			continue
		}

		if _, ok := bnsIPs[ip]; !ok {
			continue
		}

		ipVersionInfo := IPVersion{
			IP:      ip,
			Version: ipVersion,
		}
		tmpIPVersionSlice = append(tmpIPVersionSlice, ipVersionInfo)
	}

	var ips []string
	ips = liveFilter(ctx, ips)  // 实例是否存活
	ips = inBnsFilter(ctx, ips) // 是否在BNS中
	ipMaps := sliceToMap(ips)

	var ipVersionSlice []IPVersion
	for _, ipVersion := range tmpIPVersionSlice {
		if _, ok := ipMaps[ipVersion.IP]; ok {
			ipVersionSlice = append(ipVersionSlice, ipVersion)
		}
	}

	hiVersionInfo := &HiVersionInfo{
		NewVersion: version,
		IPVersions: ipVersionSlice,
	}

	hiVersionInfoByte, err := json.Marshal(hiVersionInfo)
	if err != nil {
		ctx.SLog.Warning("ufc version json marshal failed").SetErr(err).Print()
		return err
	}

	oldVersionKey := cacheutil.GetUfcOldVersionHsetName()
	if err := redis.Del(oldVersionKey); err != nil {
		ctx.SLog.Warning("ufc version del old version failed").Set("key", oldVersionKey).SetErr(err).Print()
		return err
	}

	msetMap := make(map[string]interface{})
	for _, ipVersion := range ipVersionSlice {
		msetMap[ipVersion.IP] = ipVersion.Version
	}
	if err := redis.HMSet(oldVersionKey, msetMap); err != nil {
		ctx.SLog.Warning("ufc version hmset failed").Set("key", oldVersionKey).SetErr(err).Print()
		return err
	}

	hiVersionInfoStr := string(hiVersionInfoByte)
	if len(hiVersionInfoStr) > 1800 {
		hiVersionInfoStr = hiVersionInfoStr[1:1800]
	}

	hiVersionInfoStr = "【ufc版本diff上报】\n" + hiVersionInfoStr

	sendHiOption := common.SendHiOption{
		Type:    common.SendHiTypeText,
		Content: hiVersionInfoStr,
	}
	if err := common.SendHi(conf.Application.HiWarning.URL, sendHiOption); err != nil {
		ctx.SLog.Warning("ufc version send hi failed").SetErr(err).Print()
		return err
	}

	return nil
}

type DiffWarnInfo struct {
	Incre WarnInfo `json:"incre"`
	Decre WarnInfo `json:"decre"`
}

func (s *AgentLatencyScheduler) sendDiffToHi(ctx *easy.Context, redis *easy.Redis, isOnline bool, curWarnInfo *WarnInfo) {
	diffInfo := s.getDiffWarnInfo(ctx, redis, isOnline, curWarnInfo)
	if diffInfo == nil {
		return
	}

	diffInfoByte, err := json.Marshal(diffInfo)
	if err != nil {
		ctx.SLog.Warning("ufc hi diff info  json marshal failed").SetErr(err).Print()
	}

	diffInfoStr := string(diffInfoByte)
	if len(diffInfoStr) > 1800 {
		diffInfoStr = diffInfoStr[:1800]
	}

	msg := fmt.Sprintf("【是否为沙盒上报: %v】【含opera/orp实例: %v】\n 延迟增量信息：%s\n", curWarnInfo.IsSandboxUfc, isOnline, diffInfoStr)

	sendHiOption := common.SendHiOption{
		Type:    common.SendHiTypeText,
		Content: msg,
	}
	if err := common.SendHi(conf.Application.HiWarning.URL, sendHiOption); err != nil {
		ctx.SLog.Warning("send latency diff to hi failed").Print()
	} else {
		ctx.SLog.Info("send latency diff to hi success").Print()
	}

}

// 获取增量 ip
func (s *AgentLatencyScheduler) getDiffWarnInfo(ctx *easy.Context, redis *easy.Redis, isOnline bool, curWarnInfo *WarnInfo) *DiffWarnInfo {
	// 获取完 diff 把这次最新结果写进 redis 中，防止下次 diff 重复上报
	defer func() {
		key := cacheutil.GetLastHiWarnInfoKey(isOnline)
		curWarnInfoByte, err := json.Marshal(curWarnInfo)
		if err != nil {
			ctx.SLog.Warning("marshal cur warn info failed").SetErr(err).Print()
			return
		}
		if err := redis.Set(key, string(curWarnInfoByte)); err != nil {
			ctx.SLog.Warning("set last warn info failed").Set("key", key).SetErr(err).Print()
			return
		}
	}()

	key := cacheutil.GetLastHiWarnInfoKey(isOnline)
	lastWarnInfoByte, err := redis.Get(key)
	if err != nil {
		ctx.SLog.Warning("get last warn info failed").Set("key", key).SetErr(err).Print()
		return nil
	}

	lastWarnInfo := &WarnInfo{}
	if err := json.Unmarshal([]byte(lastWarnInfoByte), &lastWarnInfo); err != nil {
		ctx.SLog.Warning("unmarshal last warn info failed").Set("key", key).SetErr(err).Print()
		return nil
	}

	Incre := WarnInfo{}
	Decre := WarnInfo{}

	{ //  curWarnInfo.UpdateFailedIPs
		curIPMap := make(map[string]struct{})
		lastIPMap := make(map[string]struct{})
		for _, ip := range curWarnInfo.UpdateFailedIPs {
			curIPMap[ip] = struct{}{}
		}
		for _, ip := range lastWarnInfo.UpdateFailedIPs {
			lastIPMap[ip] = struct{}{}
		}

		// 获取 diff
		for _, ip := range lastWarnInfo.UpdateFailedIPs {
			_, ok := curIPMap[ip]
			if !ok {
				Decre.UpdateFailedIPs = append(Decre.UpdateFailedIPs, ip)
			}
		}

		for _, ip := range curWarnInfo.UpdateFailedIPs {
			_, ok := lastIPMap[ip]
			if !ok {
				Incre.UpdateFailedIPs = append(Decre.UpdateFailedIPs, ip)
			}
		}
	}

	{ // curWarnInfo.UpdateDelayIPs, 获取 diff
		curIPMap := make(map[string]struct{})
		lastIPMap := make(map[string]struct{})
		for ip := range curWarnInfo.UpdateDelayIPs {
			curIPMap[ip] = struct{}{}
		}
		for ip := range lastWarnInfo.UpdateDelayIPs {
			lastIPMap[ip] = struct{}{}
		}

		for _, ip := range lastWarnInfo.UpdateFailedIPs {
			_, ok := curIPMap[ip]
			if !ok {
				Decre.UpdateDelayIPs[ip] = -1
			}
		}
		for _, ip := range curWarnInfo.UpdateFailedIPs {
			_, ok := lastIPMap[ip]
			if !ok {
				Incre.UpdateDelayIPs[ip] = -1
			}
		}
	}

	{ //  curWarnInfo.NotUploadIPs
		curIPMap := make(map[string]struct{})
		lastIPMap := make(map[string]struct{})
		for _, ip := range curWarnInfo.NotUploadIPs {
			curIPMap[ip] = struct{}{}
		}
		for _, ip := range lastWarnInfo.NotUploadIPs {
			lastIPMap[ip] = struct{}{}
		}

		// 获取 diff
		for _, ip := range lastWarnInfo.NotUploadIPs {
			_, ok := curIPMap[ip]
			if !ok {
				Decre.NotUploadIPs = append(Decre.NotUploadIPs, ip)
			}
		}
		for _, ip := range curWarnInfo.UpdateFailedIPs {
			_, ok := lastIPMap[ip]
			if !ok {
				Incre.NotUploadIPs = append(Decre.NotUploadIPs, ip)
			}
		}
	}

	return &DiffWarnInfo{
		Incre: Incre,
		Decre: Decre,
	}
}

func getIP(backend string) string {
	return backend
}

package worker

import (
	"fmt"
	"net"
	"os"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
)

func getMatrixID() string {
	return os.Getenv("MATRIX_INSTANCE_ID")
}

func getContainerName(instanceName string) (string, bool) {
	slice := strings.Split(instanceName, ".")
	if len(slice) != 4 {
		return instanceName, false
	}
	subString := strings.Split(slice[1], "-")
	if len(subString) != 6 {
		return instanceName, false
	}

	var subStringTmp []string
	for i, item := range subString {
		if i == 3 {
			continue
		}
		subStringTmp = append(subStringTmp, item)
	}
	subStringNew := strings.Join(subStringTmp, "-")
	slice[1] = subStringNew

	return strings.Join(slice, "."), true
}

func liveFilter(ctx *easy.Context, ips []string) []string {
	var res []string

	batchCnt := 30
	start := 0
	end := 0

	for {
		if start >= len(ips) {
			break
		}
		end = start + batchCnt
		if end >= len(ips) {
			end = len(ips)
		}

		batch := ips[start:end]
		slice := liveBatchFilter(ctx, batch, 22)
		res = append(res, slice...)

		start = start + batchCnt
	}

	return res
}

func liveBatchFilter(ctx *easy.Context, ips []string, port uint16) []string {
	var wg sync.WaitGroup
	var mu sync.Mutex
	var res []string
	for _, ip := range ips {
		wg.Add(1)
		go func(ctx *easy.Context, ip string) {
			defer wg.Done()

			backend := fmt.Sprintf("%s:%d", ip, port)
			isLive := checkTCP(ctx, backend, 2*time.Second)
			if !isLive {
				return
			}
			mu.Lock()
			res = append(res, ip)
			mu.Unlock()

		}(ctx, ip)
	}
	return res
}

func inBnsFilter(ctx *easy.Context, ips []string) []string {
	var res []string
	ipMaps := getUfcBnsIPMaps(ctx)
	for _, ip := range ips {
		_, ok := ipMaps[ip]
		if !ok {
			continue
		}
		res = append(res, ip)
	}

	return res
}

func checkTCP(ctx *easy.Context, backend string, timeout time.Duration) bool {
	conn, err := net.DialTimeout("tcp", backend, timeout)
	if err != nil {
		ctx.SLog.Warning("tcp connect failed").Set("backend", backend).SetErr(err).Print()
		return false
	}
	conn.Close()
	return true
}

func getUfcBnsIPMaps(ctx *easy.Context) map[string]struct{} {
	ufcBnsIPs := make(map[string]struct{}) // UFC BNS下的机器IP列表
	// 获取BNS下的IP列表
	var bnsList []string = conf.Application.UfcBns
	for _, bns := range bnsList {
		// 内部做了重试
		ips, err := common.GetBnsInstance(ctx, bns)
		if err != nil {
			ctx.SLog.Warning("get bns instance error").Set("bns", bns).Print()
			continue
		}

		for _, ip := range ips {
			ufcBnsIPs[ip] = struct{}{}
		}
	}
	return ufcBnsIPs
}

func sliceToMap(slice []string) map[string]struct{} {
	res := make(map[string]struct{})

	for _, item := range slice {
		res[item] = struct{}{}
	}
	return res
}

package worker

import (
	"os"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/fileutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/watchdogutil"
)

// 定时任务处理的handler
func (s *WatchDogScheduler) Handler() {
	ctx := easy.NewContext()
	// 入口handler panic捕获，提高代码健壮性，方便问题定位，请谨慎删除
	defer func() {
		if err := recover(); err != nil {
			ctx.SLog.Error("WatchDogScheduler handler panic").Set("panic", string(debug.Stack())).Print()
			return
		}
	}()

	ctx.SLog.Info("--------WatchDogScheduler handler start").Print()

	redis := easy.NewRedis(ctx, "redis")

	typeList := []string{
		watchdogutil.AlertTypeDisk,
		watchdogutil.AlertTypeNginxLog,
		watchdogutil.AlertTypeOther,
	}

	// 清楚掉缓存中的数据，缓存中的数据为了规避频繁 Hi 报警
	for _, typ := range typeList {
		// hsetName := cacheutil.GetWatchDogHSetName(typ)
		if err := s.cleanupHsetKey(ctx, redis, typ); err != nil {
			ctx.SLog.Error("cleanupHsetKey error").Set("type", typ).SetErr(err).Print()
		} else {
			ctx.SLog.Info("cleanupHsetKey success").Set("type", typ).Print()
		}
	}

	// 清楚 30 天前的 files 文件
	if err := s.cleanupOlderFile(ctx); err != nil {
		ctx.SLog.Warning("cleanupOlderFiles error").SetErr(err).Print()
	} else {
		ctx.SLog.Info("cleanupOlderFiles success").Print()
	}

	// 结构化日志打印，详情可参见https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
}

// 清楚 30 天前的 files 文件
func (s *WatchDogScheduler) cleanupOlderFile(ctx *easy.Context) error {
	// 获取30天前的时间点
	cutoffTime := time.Now().AddDate(0, 0, -30)

	// 获取日志目录
	logDir := fileutil.FileDir

	// 遍历目录
	files, err := os.ReadDir(logDir)
	if err != nil {
		ctx.SLog.Error("read log directory error").Set("dir", logDir).SetErr(err).Print()
		return err
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		// 获取文件信息
		filePath := filepath.Join(logDir, file.Name())
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			ctx.SLog.Warning("get file stat error").Set("file", file.Name()).SetErr(err).Print()
			continue
		}

		// 检查修改时间是否早于30天前
		if fileInfo.ModTime().Before(cutoffTime) {
			// 删除文件
			if err := os.Remove(filePath); err != nil {
				ctx.SLog.Warning("remove file error").Set("file", file.Name()).SetErr(err).Print()
				continue
			}
			ctx.SLog.Info("removed old log file success").Set("file", file.Name()).Print()
		}
	}

	return nil
}

func (s *WatchDogScheduler) cleanupHsetKey(ctx *easy.Context, redis *easy.Redis, typ string) error {
	hsetName := cacheutil.GetWatchDogHSetName(typ)
	resultMap, err := redis.HgetallReturnMap(hsetName)
	if err != nil {
		ctx.SLog.Error("redis hgetall error").Set("hsetName", hsetName).Set("err", err).Print()
		return err
	}

	for ip, timestampStr := range resultMap {
		isDeleteIP := s.isDeleteIP(typ, timestampStr)
		if isDeleteIP {
			err := redis.HDel(hsetName, ip)
			if err != nil {
				ctx.SLog.Error("redis hdel error").Set("hsetName", hsetName).Set("ip", ip).SetErr(err).Print()
				continue
			}
			ctx.SLog.Info("redis hdel success").Set("hsetName", hsetName).Set("ip", ip).SetErr(err).Print()
		}
	}

	return nil
}

func (s *WatchDogScheduler) isDeleteIP(typ string, timestampStr string) bool {
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return true
	}

	var interval int64 = conf.Application.Watchdog.HandleInterval.OtherInterval
	switch typ {
	case watchdogutil.AlertTypeNginxLog:
		interval = conf.Application.Watchdog.HandleInterval.UfcLogInterval
	case watchdogutil.AlertTypeDisk:
		interval = conf.Application.Watchdog.HandleInterval.DiskFullInterval
	}

	now := time.Now().Unix()
	if now-timestamp > 2*int64(interval) {
		return true
	}

	return false
}

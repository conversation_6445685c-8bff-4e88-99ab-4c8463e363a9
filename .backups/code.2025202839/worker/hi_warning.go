package worker

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"

	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/localcache"
)

func (s *HiWarningScheduler) Handler() {
	ctx := easy.NewContext()

	resMap := make(map[string]int)
	rangeFn := func(key string, val int) error {
		resMap[key] = val
		return nil
	}
	if err := localcache.Range(rangeFn); err != nil {
		ctx.SLog.Warning("range local cache failed").Print()
		return
	}
	// 清空现有的缓存
	localcache.Reset()

	if len(resMap) == 0 {
		return
	}

	sendHiInfo := SendHIInfo{
		Module: "ufc-admin-easy",
		Info:   "hash负载均衡未带key报警",
		Data:   resMap,
	}

	byt, err := json.Marshal(sendHiInfo)
	if err != nil {
		ctx.SLog.Warning("mashal hash info to json failed").Print()
		return
	}

	sendHiOption := common.SendHiOption{
		Type:    common.SendHiTypeText,
		Content: string(byt),
	}
	if err := common.SendHi(conf.Application.HiWarning.URL, sendHiOption); err != nil {
		ctx.SLog.Warning("send hi info to warning failed").Set("url", conf.Application.HiWarning.URL).Set("body", string(byt)).Print()
		return
	}
}

type SendHIInfo struct {
	Module string `json:"module"`
	Info   string `json:"info"`
	Data   map[string]int
}

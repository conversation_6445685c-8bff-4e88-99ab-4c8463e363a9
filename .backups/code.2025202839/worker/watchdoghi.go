package worker

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime/debug"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/cacheutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/common"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/conf"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/fileutil"
	"icode.baidu.com/baidu/netdisk/ufc-admin-easy/lib/watchdogutil"
)

type LogRotationInfo struct {
	IPSlice []string  `json:"ip_slice"`
	IPInfos []*IPInfo `json:"ip_infos"`
}

type IPInfo struct {
	IP        string                      `json:"ip"`
	AlertInfo *watchdogutil.WatchDogAlert `json:"alert_info"`
}

// 定时任务处理的handler
func (s *WatchDogHiScheduler) Handler() {
	ctx := easy.NewContext()
	// 入口handler panic捕获，提高代码健壮性，方便问题定位，请谨慎删除
	defer func() {
		if err := recover(); err != nil {
			ctx.SLog.Error("WatchDogScheduler handler panic").Set("panic", string(debug.Stack())).Print()
			return
		}
	}()

	ctx.SLog.Info("--------WatchDogHiScheduler handler start").Print()

	redis := easy.NewRedis(ctx, "redis")

	// 汇聚上报上一个小时收集到的报警信息并写到文件里面，并汇聚 Hi 报警
	if err := s.handleLastHourLogRotationResult(ctx, redis); err != nil {
		ctx.SLog.Warning("handleLastHourLogRotationResult error").SetErr(err).Print()
	}

}

func (s *WatchDogHiScheduler) handleLastHourLogRotationResult(ctx *easy.Context, redis *easy.Redis) error {
	logRotationInfo := &LogRotationInfo{}

	hset := cacheutil.GetUfcLogRotationHSet(cacheutil.GetLastHour())
	hsetMap, err := redis.HgetallReturnMap(hset)
	if err != nil {
		ctx.SLog.Error("get last hour log rotation hset error").Set("hsetName", hset).SetErr(err).Print()
		return err
	}

	for ip, alertInfoStr := range hsetMap {
		var alertInfo watchdogutil.WatchDogAlert
		if err := json.Unmarshal([]byte(alertInfoStr), &alertInfo); err != nil {
			ctx.SLog.Error("alert unmarshal error").SetErr(err).Print()
			continue
		}

		if watchdogutil.IsFilterMsg(&alertInfo) {
			continue
		}

		ipInfo := &IPInfo{
			IP:        ip,
			AlertInfo: &alertInfo,
		}
		logRotationInfo.IPInfos = append(logRotationInfo.IPInfos, ipInfo)
		logRotationInfo.IPSlice = append(logRotationInfo.IPSlice, ip)

	}

	if len(logRotationInfo.IPSlice) == 0 {
		ctx.SLog.Warning("log rotation no ip info").Print()
		return nil
	}

	fileName := fmt.Sprintf("ufc_log_rotation_%s.json", time.Now().Format("**********"))
	realFileName := filepath.Join(fileutil.FileDir, fileName)

	ipInfoBytes, err := json.Marshal(logRotationInfo)
	if err != nil {
		ctx.SLog.Error("json marshal error").SetErr(err).Print()
		return err
	}

	if err := fileutil.CreateAndWriteFile(realFileName, ipInfoBytes); err != nil {
		ctx.SLog.Warning("create and write log rotation  file error").SetErr(err).Print()
		return err
	} else {
		ctx.SLog.Info("create and write log rotation  file success").Set("fileName", realFileName).Print()
	}

	msg := s.formatHiMsg(fileName, watchdogutil.GetSelfMatrixHost(), logRotationInfo)

	// 发送错误的问题不用返回给上层 watchdog，这里打日志即可
	sendHiOption := common.SendHiOption{
		Type:    common.SendHiTypeMD,
		Content: msg,
	}

	// 发给 UfcAdminEasy 报警群
	if err := common.SendHi(conf.Application.HiWarning.URL, sendHiOption); err != nil {
		ctx.SLog.Error("alert send hi error").SetErr(err).Print()
	} else {
		ctx.SLog.Error("alert send hi success").Set("msg", msg).Print()
	}

	// 发给OP群
	if conf.Application.Watchdog.HiAtOpEnable && !common.IsTodayWeekend() {
		opList, err := watchdogutil.GetZhibanOPList(ctx)
		if err != nil {
			opList = []string{}
		}
		sendHiOption.AtUserIDs = opList
		sendHiOption.NeedAt = true
	}
	if err := common.SendHi(conf.Application.HiWarning.OpURL, sendHiOption); err != nil {
		ctx.SLog.Error("alert send op hi error").SetErr(err).Print()
		return err
	}
	// if err := common.SendHi(conf.Application.HiWarning.OpURL, ) 你翻翻你和正雄的聊天记录，我那个时间节点打个电话

	return nil
}

func (s *WatchDogHiScheduler) formatHiMsg(fileName string, backend string, logRotationInfo *LogRotationInfo) string {
	sopDocURL := "https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/8MCddZCpuH/SX81HQH0s5/O_LhtrhgIf6QhB"
	fileURL := fmt.Sprintf("http://%s/watchdog/nginxlog/show?filename=%s&backend=%s", "***********", fileName, backend)

	title := "Ufc Watchdog: nginx日志超过阈值告警(日志未切分)"
	matrixName := os.Getenv("MATRIX_INSTANCE_ID")
	if matrixName == "" {
		matrixName, _ = os.Hostname()
	}

	msg := fmt.Sprintf(`
## %s
**From Instance**: %s (OP无需关注该字段)  
**问题机器数**: %d
**告警时间**:  %s
### 告警详情: [点击查看详情](%s)
### 处理SOP: [点击查看详情](%s)
`, title, matrixName, len(logRotationInfo.IPSlice), time.Now().String(), fileURL, sopDocURL)

	return msg
}

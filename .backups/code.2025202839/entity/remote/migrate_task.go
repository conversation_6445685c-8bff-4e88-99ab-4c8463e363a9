/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const MigrateTaskName = "MigrateTask"

// MigrateTask请求client
type MigrateTaskClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewMigrateTaskClient(context *easy.Context) *MigrateTaskClient {
	client := &MigrateTaskClient{}
	client.Name = MigrateTaskName
	client.Context = context
	return client
}

// post 参数封装结构
type MigrateTaskPut struct {
	InstanceName             string `json:"instance_name"`
	NeedHealthCheck          int    `json:"need_health_check"`
	NeedHealthPrepare        int    `json:"need_health_prepare"`
	NeedProcessServiceStatus int    `json:"need_process_service_status"`
	NeedResourcePrepare      int    `json:"need_resource_prepare"`
	Username                 string `json:"username"`
	Owner                    string `json:"owner"`
}

// header 参数封装结构
type MigrateTaskHeader struct {
	Authorization string `json:"Authorization"`
	ContentType   string `json:"Content-Type"`
}

// 返回结果封装结构
type MigrateTaskResultData struct {
	easy.BaseDto
	MigrateReason            string      `json:"migrate_reason"`
	CreateTime               string      `json:"create_time"`
	ShardID                  uint32      `json:"shard_id"`
	IspName                  string      `json:"isp_name"`
	InstanceStatus           string      `json:"instance_status"`
	EndTime                  interface{} `json:"end_time"`
	PlatformName             string      `json:"platform_name"`
	TaskType                 uint32      `json:"task_type"`
	TaskProcess              string      `json:"task_process"`
	ClusterName              string      `json:"cluster_name"`
	NeedHealthPrepare        uint32      `json:"need_health_prepare"`
	ProductlineName          string      `json:"productline_name"`
	ModifyTime               string      `json:"modify_time"`
	IsPaused                 uint32      `json:"is_paused"`
	TerminalProcess          string      `json:"terminal_process"`
	StatusDetail             string      `json:"status_detail"`
	NeedCallBack             uint32      `json:"need_call_back"`
	FinalProcess             string      `json:"final_process"`
	MigrateTaskID            uint64      `json:"migrate_task_id"`
	SourceHostIP             uint64      `json:"source_host_ip"`
	TargetClusterName        string      `json:"target_cluster_name"`
	CreateUsername           string      `json:"create_username"`
	NeedResourcePrepare      uint32      `json:"need_resource_prepare"`
	InstanceName             string      `json:"instance_name"`
	InstanceType             uint32      `json:"instance_type"`
	NeedHealthCheck          uint32      `json:"need_health_check"`
	NeedMigrateAcrossCluster uint32      `json:"need_migrate_across_cluster"`
	ApplicationName          string      `json:"application_name"`
	TargetHostIP             int32       `json:"target_host_ip"`
}

func (t *MigrateTaskResultData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type MigrateTaskResult struct {
	easy.UfcResult
	Data *MigrateTaskResultData `json:"data"`
}

func (t *MigrateTaskResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *MigrateTaskClient) Do(
	body *MigrateTaskPut,
	header *MigrateTaskHeader) (*MigrateTaskResult, error) {
	t.Header = header
	t.Body = body
	res := &MigrateTaskResult{}
	err := t.UfcRequest(res)
	return res, err
}

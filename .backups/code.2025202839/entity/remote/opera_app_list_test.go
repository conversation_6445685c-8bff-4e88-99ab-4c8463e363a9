package remote

import (
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_client/client_http/http_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"
)

// 本地桩数据mock
type mockHTTPLocalOperaAppList struct {
	host string
}

// http请求驱动mock
func (s mockHTTPLocalOperaAppList) Do(request *http.Request) (*http.Response, error) {
	// 构建一个假的 HTTP 请求和响应数据
	// 构建响应的 Body 数据，请自定义构建自定义桩数据满足业务测试需求
	responseBody := `{
	}`
	// 构建 Response 对象
	response := &http.Response{
		Status:     "200 OK",
		StatusCode: http.StatusOK,
		Proto:      "HTTP/1.1",
		ProtoMajor: 1,
		ProtoMinor: 1,
		Header:     http.Header{},
		Body:       io.NopCloser(strings.NewReader(responseBody)), // Use ioutil.NopCloser to create an io.ReadCloser
		Request:    request,
	}
	return response, nil
}

// RPC:OperaAppList 测试入口
func TestOperaAppListDo(t *testing.T) {
	// 初始化Client
	client := initOperaAppListClient(t)
	// 本地mock http 驱动
	// 可以随意指定，本地不走直连请求
	err := http_mock.Registry("operaAppList", mockHTTPLocalOperaAppList{"http://**********:8057"})
	assert.Nil(t, err)

	// 创建测试用的请求参数
	query := &OperaAppListGet{
		// 在这里设置测试用的查询参数
	}

	header := &OperaAppListHeader{
		// 在这里设置测试用的头部参数
	}

	// 调用被测试的函数
	err = client.Do(query, header)

	t.Logf("response:%+v", client.Response)
	assert.Nil(t, err)

	// 请在这里添加更多的断言来验证 result 是否符合预期
}

// {item.structName} MarshalJSON测试入口返回结果封装结构

// 初始化Client
func initOperaAppListClient(t *testing.T) *OperaAppListClient {
	// 初始化配置文件,如果在根目录启动则不用设置
	config_mock.SetConfRootPath("../../conf")
	err := easy.Init("")
	assert.Nil(t, err)
	// 创建一个测试用的上下文
	context := easy.NewContext()
	// 创建一个 MetabyidClient 实例
	client := NewOperaAppListClient(context)
	return client
}
